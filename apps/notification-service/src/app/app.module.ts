import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';

import { AppController } from './app.controller';
import { AppService } from './app.service';
import { NotificationsController } from './controllers/notifications.controller';
import { HealthController } from './controllers/health.controller';
import { NotificationService } from './services/notification.service';
import { HealthService } from './services/health.service';
import { DatabaseModule } from '../../../../libs/database/database.module';
import { QueueModule } from '../../../../libs/queue/queue.module';
import { NotificationProvidersModule } from '../../../../libs/notifications/notification-providers.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    ScheduleModule.forRoot(),
    DatabaseModule,
    QueueModule,
    NotificationProvidersModule,
  ],
  controllers: [AppController, NotificationsController, HealthController],
  providers: [AppService, NotificationService, HealthService],
})
export class AppModule {}
