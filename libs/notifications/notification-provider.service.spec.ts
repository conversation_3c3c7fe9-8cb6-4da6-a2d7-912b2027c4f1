import { Test, TestingModule } from '@nestjs/testing';
import { FcmProvider } from './providers/fcm/fcm.provider';
import { ApnsProvider } from './providers/apns/apns.provider';
import { EmailProvider } from './providers/email/email.provider';
import { SmsProvider } from './providers/sms/sms.provider';
import { WnsProvider } from './providers/wns/wns.provider';
import { NotificationProviderService } from './notification-provider.service';
import { NotificationChannel } from '../shared/enums';
describe('NotificationProviderService', () => {
  let service: NotificationProviderService;
  let fcmProvider: FcmProvider;
  let emailProvider: EmailProvider;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        NotificationProviderService,
        {
          provide: FcmProvider,
          useValue: {
            channel: NotificationChannel.Fcm,
            send: jest.fn(),
            validateRecipient: jest.fn(),
            isHealthy: jest.fn(),
          },
        },
        {
          provide: ApnsProvider,
          useValue: {
            channel: NotificationChannel.Apns,
            send: jest.fn(),
            validateRecipient: jest.fn(),
            isHealthy: jest.fn(),
          },
        },
        {
          provide: EmailProvider,
          useValue: {
            channel: NotificationChannel.Email,
            send: jest.fn(),
            validateRecipient: jest.fn(),
            isHealthy: jest.fn(),
          },
        },
        {
          provide: SmsProvider,
          useValue: {
            channel: NotificationChannel.Sms,
            send: jest.fn(),
            validateRecipient: jest.fn(),
            isHealthy: jest.fn(),
          },
        },
        {
          provide: WnsProvider,
          useValue: {
            channel: NotificationChannel.Wns,
            send: jest.fn(),
            validateRecipient: jest.fn(),
            isHealthy: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<NotificationProviderService>(NotificationProviderService);
    fcmProvider = module.get<FcmProvider>(FcmProvider);
    emailProvider = module.get<EmailProvider>(EmailProvider);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('sendNotification', () => {
    it('should send notification using correct provider', async () => {
      const payload = {
        title: 'Test Title',
        body: 'Test Body',
      };

      const recipient = {
        id: 'test-recipient',
        channel: NotificationChannel.Fcm,
        address: 'fcm-token',
      };

      const mockResult = {
        id: 'result-id',
        recipientId: recipient.id,
        channel: NotificationChannel.Fcm,
        status: 'sent' as any,
      };

      jest.spyOn(fcmProvider, 'send').mockResolvedValue(mockResult);

      const result = await service.sendNotification(payload, recipient);

      expect(fcmProvider.send).toHaveBeenCalledWith(payload, recipient);
      expect(result).toEqual(mockResult);
    });

    it('should throw error for unsupported channel', async () => {
      const payload = {
        title: 'Test Title',
        body: 'Test Body',
      };

      const recipient = {
        id: 'test-recipient',
        channel: 'UNSUPPORTED' as any,
        address: 'token',
      };

      await expect(service.sendNotification(payload, recipient)).rejects.toThrow(
        'Unsupported notification channel'
      );
    });
  });

  describe('sendBulkNotifications', () => {
    it('should send notifications to multiple recipients', async () => {
      const payload = {
        title: 'Test Title',
        body: 'Test Body',
      };

      const recipients = [
        {
          id: 'recipient-1',
          channel: NotificationChannel.Fcm,
          address: 'fcm-token',
        },
        {
          id: 'recipient-2',
          channel: NotificationChannel.Email,
          address: '<EMAIL>',
        },
      ];

      const mockResults = [
        {
          id: 'result-1',
          recipientId: 'recipient-1',
          channel: NotificationChannel.Fcm,
          status: 'sent' as any,
        },
        {
          id: 'result-2',
          recipientId: 'recipient-2',
          channel: NotificationChannel.Email,
          status: 'sent' as any,
        },
      ];

      jest.spyOn(fcmProvider, 'send').mockResolvedValue(mockResults[0]);
      jest.spyOn(emailProvider, 'send').mockResolvedValue(mockResults[1]);

      const results = await service.sendBulkNotifications(payload, recipients);

      expect(results).toHaveLength(2);
      expect(fcmProvider.send).toHaveBeenCalledWith(payload, recipients[0]);
      expect(emailProvider.send).toHaveBeenCalledWith(payload, recipients[1]);
    });
  });

  describe('validateRecipients', () => {
    it('should separate valid and invalid recipients', () => {
      const recipients = [
        {
          id: 'recipient-1',
          channel: NotificationChannel.Fcm,
          address: 'fcm-token',
        },
        {
          id: 'recipient-2',
          channel: NotificationChannel.Email,
          address: 'invalid-email',
        },
      ];

      jest.spyOn(fcmProvider, 'validateRecipient').mockReturnValue(true);
      jest.spyOn(emailProvider, 'validateRecipient').mockReturnValue(false);

      const result = service.validateRecipients(recipients);

      expect(result.valid).toHaveLength(1);
      expect(result.invalid).toHaveLength(1);
      expect(result.valid[0].id).toBe('recipient-1');
      expect(result.invalid[0].id).toBe('recipient-2');
    });
  });

  describe('getHealthStatus', () => {
    it('should return health status for all providers', async () => {
      jest.spyOn(fcmProvider, 'isHealthy').mockResolvedValue(true);
      jest.spyOn(emailProvider, 'isHealthy').mockResolvedValue(false);

      const healthStatus = await service.getHealthStatus();

      expect(healthStatus[NotificationChannel.Fcm]).toBe(true);
      expect(healthStatus[NotificationChannel.Email]).toBe(false);
    });
  });

  describe('getSupportedChannels', () => {
    it('should return all supported channels', () => {
      const channels = service.getSupportedChannels();

      expect(channels).toContain(NotificationChannel.Fcm);
      expect(channels).toContain(NotificationChannel.Email);
      expect(channels).toContain(NotificationChannel.Sms);
      expect(channels).toContain(NotificationChannel.Apns);
      expect(channels).toContain(NotificationChannel.Wns);
    });
  });
});
