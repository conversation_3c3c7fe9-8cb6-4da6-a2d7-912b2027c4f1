import { Injectable, Logger } from '@nestjs/common';
import type {
  INotificationProvider,
  NotificationPayload,
  NotificationRecipient,
  NotificationResult,
} from '../shared/interfaces';
import { FcmProvider, ApnsProvider, EmailProvider, SmsProvider, WnsProvider } from './providers';
import { NotificationChannel } from '../shared/enums';

@Injectable()
export class NotificationProviderService {
  private readonly logger = new Logger(NotificationProviderService.name);
  private readonly providers = new Map<NotificationChannel, INotificationProvider>();

  constructor(
    private fcmProvider: FcmProvider,
    private apnsProvider: ApnsProvider,
    private emailProvider: EmailProvider,
    private smsProvider: SmsProvider,
    private wnsProvider: WnsProvider
  ) {
    this.initializeProviders();
  }

  private initializeProviders(): void {
    this.providers.set(NotificationChannel.Fcm, this.fcmProvider);
    this.providers.set(NotificationChannel.Apns, this.apnsProvider);
    this.providers.set(NotificationChannel.Email, this.emailProvider);
    this.providers.set(NotificationChannel.Sms, this.smsProvider);
    this.providers.set(NotificationChannel.Wns, this.wnsProvider);

    this.logger.log('Notification providers initialized');
  }

  async sendNotification(
    payload: NotificationPayload,
    recipient: NotificationRecipient
  ): Promise<NotificationResult> {
    const provider = this.providers.get(recipient.channel);

    if (!provider) {
      this.logger.error(`No provider found for channel: ${recipient.channel}`);
      throw new Error(`Unsupported notification channel: ${recipient.channel}`);
    }

    try {
      return await provider.send(payload, recipient);
    } catch (error) {
      this.logger.error(`Failed to send notification via ${recipient.channel}`, error);
      throw error;
    }
  }

  async sendBulkNotifications(
    payload: NotificationPayload,
    recipients: NotificationRecipient[]
  ): Promise<NotificationResult[]> {
    const results: NotificationResult[] = [];

    // Group recipients by channel for efficient processing
    const recipientsByChannel = this.groupRecipientsByChannel(recipients);

    for (const [channel, channelRecipients] of recipientsByChannel) {
      const provider = this.providers.get(channel);

      if (!provider) {
        this.logger.warn(`No provider found for channel: ${channel}`);
        continue;
      }

      // Send notifications concurrently for each channel
      const channelPromises = channelRecipients.map(recipient =>
        provider.send(payload, recipient).catch(error => {
          this.logger.error(`Failed to send to ${recipient.id}`, error);
          return {
            id: `error-${recipient.id}`,
            recipientId: recipient.id,
            channel: recipient.channel,
            status: 'failed' as any,
            failureReason: error.message,
          } as NotificationResult;
        })
      );

      const channelResults = await Promise.all(channelPromises);
      results.push(...channelResults);
    }

    return results;
  }

  private groupRecipientsByChannel(
    recipients: NotificationRecipient[]
  ): Map<NotificationChannel, NotificationRecipient[]> {
    const grouped = new Map<NotificationChannel, NotificationRecipient[]>();

    for (const recipient of recipients) {
      if (!grouped.has(recipient.channel)) {
        grouped.set(recipient.channel, []);
      }
      grouped.get(recipient.channel)!.push(recipient);
    }

    return grouped;
  }

  getProvider(channel: NotificationChannel): INotificationProvider | undefined {
    return this.providers.get(channel);
  }

  async getHealthStatus(): Promise<Record<NotificationChannel, boolean>> {
    const healthStatus: Record<string, boolean> = {};

    const healthChecks = Array.from(this.providers.entries()).map(async ([channel, provider]) => {
      try {
        const isHealthy = await provider.isHealthy();
        healthStatus[channel] = isHealthy;
      } catch (error) {
        this.logger.error(`Health check failed for ${channel}`, error);
        healthStatus[channel] = false;
      }
    });

    await Promise.all(healthChecks);

    return healthStatus as Record<NotificationChannel, boolean>;
  }

  getSupportedChannels(): NotificationChannel[] {
    return Array.from(this.providers.keys());
  }

  validateRecipient(recipient: NotificationRecipient): boolean {
    const provider = this.providers.get(recipient.channel);
    return provider ? provider.validateRecipient(recipient) : false;
  }

  validateRecipients(recipients: NotificationRecipient[]): {
    valid: NotificationRecipient[];
    invalid: NotificationRecipient[];
  } {
    const valid: NotificationRecipient[] = [];
    const invalid: NotificationRecipient[] = [];

    for (const recipient of recipients) {
      if (this.validateRecipient(recipient)) {
        valid.push(recipient);
      } else {
        invalid.push(recipient);
      }
    }

    return { valid, invalid };
  }
}
