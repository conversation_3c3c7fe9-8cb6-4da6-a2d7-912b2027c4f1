import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as apn from 'apn';
import {
  NotificationChannel,
  NotificationStatus,
  type NotificationPayload,
  type NotificationRecipient,
  type NotificationResult,
} from '../../../shared';
import { NotificationProvider } from '../notification.provider';

@Injectable()
export class ApnsProvider extends NotificationProvider {
  readonly channel = NotificationChannel.Apns;
  private provider: apn.Provider;

  constructor(private configService: ConfigService) {
    super();
    this.initializeApns();
  }

  private initializeApns(): void {
    try {
      const keyId = this.configService.get<string>('APNS_KEY_ID');
      const teamId = this.configService.get<string>('APNS_TEAM_ID');
      const privateKey = this.configService.get<string>('APNS_PRIVATE_KEY');
      const bundleId = this.configService.get<string>('APNS_BUNDLE_ID');
      const production = this.configService.get<boolean>('APNS_PRODUCTION', false);

      if (!keyId || !teamId || !privateKey || !bundleId) {
        this.logger.warn('APNS configuration is incomplete. APNS provider will be disabled.');
        return;
      }

      const options: apn.ProviderOptions = {
        token: {
          key: privateKey,
          keyId: keyId,
          teamId: teamId,
        },
        production: production,
      };

      this.provider = new apn.Provider(options);
      this.logger.log('APNS provider initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize APNS provider', error);
    }
  }

  async send(
    payload: NotificationPayload,
    recipient: NotificationRecipient
  ): Promise<NotificationResult> {
    try {
      if (!this.provider) {
        throw new Error('APNS provider not initialized');
      }

      if (!this.validatePayload(payload) || !this.validateRecipient(recipient)) {
        throw new Error('Invalid payload or recipient');
      }

      const sanitizedPayload = this.sanitizePayload(payload);
      const bundleId = this.configService.get<string>('APNS_BUNDLE_ID');

      if (!bundleId) {
        throw new Error('APNS bundle ID not configured');
      }

      const notification = new apn.Notification();
      notification.topic = bundleId;
      notification.alert = {
        title: sanitizedPayload.title,
        body: sanitizedPayload.body,
      };
      notification.badge = 1;
      notification.sound = 'default';
      notification.payload = {
        ...sanitizedPayload.data,
        actionUrl: sanitizedPayload.actionUrl,
      };

      if (sanitizedPayload.imageUrl) {
        notification.mutableContent = true;
        notification.payload.imageUrl = sanitizedPayload.imageUrl;
      }

      const result = await this.provider.send(notification, recipient.address);

      if (result.failed && result.failed.length > 0) {
        const failure = result.failed[0];
        throw new Error(`APNS send failed: ${failure.error || failure.status}`);
      }

      this.logger.log(`APNS notification sent successfully to ${recipient.address}`);

      return this.createResult(recipient.id, NotificationStatus.Sent, {
        apnsToken: recipient.address,
        sent: result.sent?.length || 0,
        failed: result.failed?.length || 0,
      });
    } catch (error) {
      return this.handleError(error, recipient.id);
    }
  }

  validateRecipient(recipient: NotificationRecipient): boolean {
    return (
      recipient.channel === NotificationChannel.Apns &&
      !!recipient.address &&
      recipient.address.length === 64
    ); // APNS device tokens are 64 characters
  }

  isConfigured(): boolean {
    return !!this.provider;
  }

  async isHealthy(): Promise<boolean> {
    try {
      return !!this.provider;
    } catch (error) {
      this.logger.error('APNS health check failed', error);
      return false;
    }
  }

  async shutdown(): Promise<void> {
    if (this.provider) {
      this.provider.shutdown();
      this.logger.log('APNS provider shutdown completed');
    }
  }
}
