import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { type AxiosInstance } from 'axios';
import {
  NotificationChannel,
  NotificationStatus,
  type NotificationPayload,
  type NotificationRecipient,
  type NotificationResult,
} from '../../../shared';
import { NotificationProvider } from '../notification.provider';

interface WnsAccessToken {
  access_token: string;
  token_type: string;
  expires_in: number;
  obtained_at: number;
}

@Injectable()
export class WnsProvider extends NotificationProvider {
  readonly channel = NotificationChannel.Wns;
  private httpClient: AxiosInstance;
  private accessToken: WnsAccessToken | null = null;
  private clientId: string;
  private clientSecret: string;

  constructor(private configService: ConfigService) {
    super();
    this.initializeWns();
  }

  private initializeWns(): void {
    try {
      this.clientId = this.configService.get<string>('WNS_CLIENT_ID');
      this.clientSecret = this.configService.get<string>('WNS_CLIENT_SECRET');

      if (!this.clientId || !this.clientSecret) {
        this.logger.warn('WNS configuration is incomplete. WNS provider will be disabled.');
        return;
      }

      this.httpClient = axios.create({
        timeout: 10000,
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });

      this.logger.log('WNS provider initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize WNS provider', error);
    }
  }

  private async getAccessToken(): Promise<string> {
    try {
      // Check if we have a valid token
      if (this.accessToken && this.isTokenValid()) {
        return this.accessToken.access_token;
      }

      // Request new access token
      const response = await this.httpClient.post(
        'https://login.live.com/accesstoken.srf',
        new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: this.clientId,
          client_secret: this.clientSecret,
          scope: 'notify.windows.com',
        }).toString()
      );

      this.accessToken = {
        ...response.data,
        obtained_at: Date.now(),
      };

      return this.accessToken.access_token;
    } catch (error) {
      this.logger.error('Failed to obtain WNS access token', error);
      throw new Error('Failed to authenticate with WNS');
    }
  }

  private isTokenValid(): boolean {
    if (!this.accessToken) return false;

    const expirationTime = this.accessToken.obtained_at + this.accessToken.expires_in * 1000;
    const bufferTime = 5 * 60 * 1000; // 5 minutes buffer

    return Date.now() < expirationTime - bufferTime;
  }

  async send(
    payload: NotificationPayload,
    recipient: NotificationRecipient
  ): Promise<NotificationResult> {
    try {
      if (!this.httpClient) {
        throw new Error('WNS provider not initialized');
      }

      if (!this.validatePayload(payload) || !this.validateRecipient(recipient)) {
        throw new Error('Invalid payload or recipient');
      }

      const sanitizedPayload = this.sanitizePayload(payload);
      const accessToken = await this.getAccessToken();

      // Create toast notification XML
      const toastXml = this.createToastNotification(sanitizedPayload);

      const response = await this.httpClient.post(
        recipient.address, // WNS channel URI
        toastXml,
        {
          headers: {
            Authorization: `Bearer ${accessToken}`,
            'Content-Type': 'text/xml',
            'X-WNS-Type': 'wns/toast',
            'X-WNS-RequestForStatus': 'true',
          },
        }
      );

      const notificationStatus = response.headers['x-wns-notificationstatus'];
      const deviceConnectionStatus = response.headers['x-wns-deviceconnectionstatus'];

      this.logger.log(`WNS notification sent successfully. Status: ${notificationStatus}`);

      return this.createResult(recipient.id, NotificationStatus.Sent, {
        wnsChannelUri: recipient.address,
        notificationStatus,
        deviceConnectionStatus,
        responseHeaders: response.headers,
      });
    } catch (error) {
      return this.handleError(error, recipient.id);
    }
  }

  private createToastNotification(payload: NotificationPayload): string {
    let toastXml = `
      <toast>
        <visual>
          <binding template="ToastGeneric">
            <text>${this.escapeXml(payload.title)}</text>
            <text>${this.escapeXml(payload.body)}</text>
    `;

    if (payload.imageUrl) {
      toastXml += `<image src="${this.escapeXml(
        payload.imageUrl
      )}" placement="appLogoOverride" hint-crop="circle"/>`;
    }

    toastXml += `
          </binding>
        </visual>
    `;

    if (payload.actionUrl) {
      toastXml += `
        <actions>
          <action content="Open" arguments="${this.escapeXml(
            payload.actionUrl
          )}" activationType="protocol"/>
        </actions>
      `;
    }

    toastXml += `</toast>`;

    return toastXml;
  }

  private escapeXml(text: string): string {
    return text
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#39;');
  }

  validateRecipient(recipient: NotificationRecipient): boolean {
    // WNS channel URIs should be valid URLs
    const urlRegex = /^https:\/\/.*\.notify\.windows\.com\/.*/;
    return (
      recipient.channel === NotificationChannel.Wns &&
      !!recipient.address &&
      urlRegex.test(recipient.address)
    );
  }

  isConfigured(): boolean {
    return !!(this.httpClient && this.clientId && this.clientSecret);
  }

  async isHealthy(): Promise<boolean> {
    try {
      if (!this.httpClient || !this.clientId || !this.clientSecret) {
        return false;
      }

      // Try to get access token to verify credentials
      await this.getAccessToken();
      return true;
    } catch (error) {
      this.logger.error('WNS health check failed', error);
      return false;
    }
  }
}
