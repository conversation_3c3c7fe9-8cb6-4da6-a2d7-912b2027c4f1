import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as admin from 'firebase-admin';
import {
  NotificationChannel,
  NotificationStatus,
  type NotificationPayload,
  type NotificationRecipient,
  type NotificationResult,
} from '../../../shared';
import { NotificationProvider } from '../notification.provider';

@Injectable()
export class FcmProvider extends NotificationProvider {
  readonly channel = NotificationChannel.Fcm;
  private app: admin.app.App;

  constructor(private configService: ConfigService) {
    super();
    this.initializeFirebase();
  }

  private initializeFirebase(): void {
    try {
      const projectId = this.configService.get<string>('FCM_PROJECT_ID');
      const privateKey = this.configService.get<string>('FCM_PRIVATE_KEY')?.replace(/\\n/g, '\n');
      const clientEmail = this.configService.get<string>('FCM_CLIENT_EMAIL');

      if (!projectId || !privateKey || !clientEmail) {
        this.logger.warn('FCM configuration is incomplete. FCM provider will be disabled.');
        return;
      }

      this.app = admin.initializeApp(
        {
          credential: admin.credential.cert({
            projectId,
            privateKey,
            clientEmail,
          }),
        },
        'fcm-app'
      );

      this.logger.log('Firebase Admin SDK initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Firebase Admin SDK', error);
    }
  }

  async send(
    payload: NotificationPayload,
    recipient: NotificationRecipient
  ): Promise<NotificationResult> {
    try {
      if (!this.app) {
        throw new Error('Firebase Admin SDK not initialized');
      }

      if (!this.validatePayload(payload) || !this.validateRecipient(recipient)) {
        throw new Error('Invalid payload or recipient');
      }

      const sanitizedPayload = this.sanitizePayload(payload);

      const message: admin.messaging.Message = {
        token: recipient.address,
        notification: {
          title: sanitizedPayload.title,
          body: sanitizedPayload.body,
          imageUrl: sanitizedPayload.imageUrl,
        },
        data: {
          ...sanitizedPayload.data,
          actionUrl: sanitizedPayload.actionUrl || '',
        },
        android: {
          priority: 'high',
          notification: {
            clickAction: sanitizedPayload.actionUrl,
            icon: 'ic_notification',
            color: '#2196F3',
          },
        },
        apns: {
          payload: {
            aps: {
              badge: 1,
              sound: 'default',
            },
          },
        },
        webpush: {
          notification: {
            icon: '/icon-192x192.png',
            badge: '/badge-72x72.png',
            actions: sanitizedPayload.actionUrl
              ? [
                  {
                    action: 'open',
                    title: 'Open',
                  },
                ]
              : undefined,
          },
          fcmOptions: {
            link: sanitizedPayload.actionUrl,
          },
        },
      };

      const response = await admin.messaging(this.app).send(message);

      this.logger.log(`FCM notification sent successfully: ${response}`);

      return this.createResult(recipient.id, NotificationStatus.Sent, {
        messageId: response,
        fcmToken: recipient.address,
      });
    } catch (error) {
      return this.handleError(error, recipient.id);
    }
  }

  validateRecipient(recipient: NotificationRecipient): boolean {
    return (
      recipient.channel === NotificationChannel.Fcm &&
      !!recipient.address &&
      recipient.address.length > 0
    );
  }

  isConfigured(): boolean {
    return !!this.app;
  }

  async isHealthy(): Promise<boolean> {
    try {
      if (!this.app) {
        return false;
      }

      // Try to get the app instance to verify connection
      const app = admin.app('fcm-app');
      return !!app;
    } catch (error) {
      this.logger.error('FCM health check failed', error);
      return false;
    }
  }
}
