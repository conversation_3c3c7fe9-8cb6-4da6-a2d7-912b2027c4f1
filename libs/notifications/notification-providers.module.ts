import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { FcmProvider } from './providers/fcm/fcm.provider';
import { ApnsProvider } from './providers/apns/apns.provider';
import { EmailProvider } from './providers/email/email.provider';
import { SmsProvider } from './providers/sms/sms.provider';
import { WnsProvider } from './providers/wns/wns.provider';
import { NotificationProviderService } from './notification-provider.service';

@Module({
  imports: [ConfigModule],
  providers: [
    FcmProvider,
    ApnsProvider,
    EmailProvider,
    SmsProvider,
    WnsProvider,
    NotificationProviderService,
  ],
  exports: [
    FcmProvider,
    ApnsProvider,
    EmailProvider,
    SmsProvider,
    WnsProvider,
    NotificationProviderService,
  ],
})
export class NotificationProvidersModule {}
