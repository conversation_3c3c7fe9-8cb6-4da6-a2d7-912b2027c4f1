import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsDateString,
  IsEnum,
  IsObject,
  IsOptional,
  IsString,
  ValidateNested,
} from 'class-validator';
import { NotificationChannel } from '../enums';

import { NotificationPayloadDto } from './notification-payload.dto';
import { NotificationRecipientDto } from './notification-recipient.dto';
import { NotificationPriority } from '../enums/notification-priority.enum';

export class CreateNotificationDto {
  @ApiPropertyOptional({ description: 'Template ID to use for notification' })
  @IsOptional()
  @IsString()
  templateId?: string;

  @ApiProperty({ type: NotificationPayloadDto, description: 'Notification payload' })
  @ValidateNested()
  @Type(() => NotificationPayloadDto)
  payload: NotificationPayloadDto;

  @ApiProperty({ type: [NotificationRecipientDto], description: 'List of recipients' })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => NotificationRecipientDto)
  recipients: NotificationRecipientDto[];

  @ApiProperty({
    enum: NotificationChannel,
    isArray: true,
    description: 'Channels to send notification through',
  })
  @IsArray()
  @IsEnum(NotificationChannel, { each: true })
  channels: NotificationChannel[];

  @ApiProperty({ enum: NotificationPriority, description: 'Notification priority' })
  @IsEnum(NotificationPriority)
  priority: NotificationPriority;

  @ApiPropertyOptional({ description: 'Schedule notification for future delivery' })
  @IsOptional()
  @IsDateString()
  scheduledAt?: string;

  @ApiPropertyOptional({ description: 'Notification expiration time' })
  @IsOptional()
  @IsDateString()
  expiresAt?: string;

  @ApiPropertyOptional({ description: 'Additional metadata' })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({ description: 'User who created the notification' })
  @IsOptional()
  @IsString()
  createdBy?: string;
}
