import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsEnum, IsOptional, IsString, ValidateNested } from 'class-validator';
import { NotificationChannel } from '../enums';
import { NotificationPayloadDto } from './notification-payload.dto';

export class CreateTemplateDto {
  @ApiProperty({ description: 'Template name' })
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Template description' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ enum: NotificationChannel, isArray: true, description: 'Supported channels' })
  @IsArray()
  @IsEnum(NotificationChannel, { each: true })
  channels: NotificationChannel[];

  @ApiProperty({ type: NotificationPayloadDto, description: 'Template payload' })
  @ValidateNested()
  @Type(() => NotificationPayloadDto)
  payload: NotificationPayloadDto;

  @ApiPropertyOptional({ description: 'Template variables for dynamic content' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  variables?: string[];
}
