import { NotificationChannel, NotificationPriority, NotificationStatus } from "../enums";

export interface NotificationPayload {
  title: string;
  body: string;
  data?: Record<string, any>;
  imageUrl?: string;
  actionUrl?: string;
}

export interface NotificationRecipient {
  id: string;
  channel: NotificationChannel;
  address: string; // email, phone, device token, etc.
  metadata?: Record<string, any>;
}

export interface NotificationRequest {
  id?: string;
  templateId?: string;
  payload: NotificationPayload;
  recipients: NotificationRecipient[];
  channels: NotificationChannel[];
  priority: NotificationPriority;
  scheduledAt?: Date;
  expiresAt?: Date;
  metadata?: Record<string, any>;
  createdBy?: string;
}

export interface NotificationResult {
  id: string;
  recipientId: string;
  channel: NotificationChannel;
  status: NotificationStatus;
  sentAt?: Date;
  deliveredAt?: Date;
  failureReason?: string;
  metadata?: Record<string, any>;
}

export interface NotificationTemplate {
  id: string;
  name: string;
  description?: string;
  channels: NotificationChannel[];
  payload: NotificationPayload;
  variables?: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

