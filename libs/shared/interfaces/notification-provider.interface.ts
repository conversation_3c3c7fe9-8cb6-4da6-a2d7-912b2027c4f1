import { NotificationChannel } from '../enums';
import type { NotificationPayload, NotificationRecipient, NotificationResult } from '../interfaces';

export interface INotificationProvider {
  readonly channel: NotificationChannel;
  send(payload: NotificationPayload, recipient: NotificationRecipient): Promise<NotificationResult>;
  validateRecipient(recipient: NotificationRecipient): boolean;
  isHealthy(): Promise<boolean>;
}
