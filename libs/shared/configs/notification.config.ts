export interface NotificationConfig {
  fcm?: {
    projectId: string;
    privateKey: string;
    clientEmail: string;
  };
  apns?: {
    keyId: string;
    teamId: string;
    privateKey: string;
    bundleId: string;
    production: boolean;
  };
  email?: {
    host: string;
    port: number;
    secure: boolean;
    auth: {
      user: string;
      pass: string;
    };
    from: string;
  };
  sms?: {
    accountSid: string;
    authToken: string;
    fromNumber: string;
  };
  wns?: {
    clientId: string;
    clientSecret: string;
  };
}
