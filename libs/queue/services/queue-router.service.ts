import { Inject, Injectable, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { NotificationChannel } from '../../shared';
import { NotificationQueueMessage } from '../interfaces/queue-message.interface';
import {
  APNS_QUEUE,
  EMAIL_QUEUE,
  FCM_QUEUE,
  NOTIFICATION_DLQ,
  NOTIFICATION_RESULT_QUEUE,
  NOTIFICATION_RETRY_QUEUE,
  SMS_QUEUE,
  WNS_QUEUE,
} from '../queue.module';

@Injectable()
export class QueueRouterService {
  private readonly logger = new Logger(QueueRouterService.name);

  constructor(
    @Inject(FCM_QUEUE) private readonly fcmQueue: ClientProxy,
    @Inject(APNS_QUEUE) private readonly apnsQueue: ClientProxy,
    @Inject(EMAIL_QUEUE) private readonly emailQueue: ClientProxy,
    @Inject(SMS_QUEUE) private readonly smsQueue: ClientProxy,
    @Inject(WNS_QUEUE) private readonly wnsQueue: ClientProxy,
    @Inject(NOTIFICATION_RESULT_QUEUE) private readonly resultQueue: ClientProxy,
    @Inject(NOTIFICATION_RETRY_QUEUE) private readonly retryQueue: ClientProxy,
    @Inject(NOTIFICATION_DLQ) private readonly dlqQueue: ClientProxy
  ) {}

  /**
   * Route notification message to the appropriate channel queue
   */
  async routeNotification(message: NotificationQueueMessage): Promise<void> {
    const { notification } = message;

    // Group recipients by channel
    const recipientsByChannel = this.groupRecipientsByChannel(notification.recipients);

    // Send to each channel queue
    for (const [channel, recipients] of recipientsByChannel.entries()) {
      const channelMessage: NotificationQueueMessage = {
        ...message,
        notification: {
          ...notification,
          recipients,
          channels: [channel],
        },
      };

      await this.sendToChannelQueue(channel, channelMessage);
    }
  }

  /**
   * Send message to specific channel queue
   */
  private async sendToChannelQueue(
    channel: NotificationChannel,
    message: NotificationQueueMessage
  ): Promise<void> {
    try {
      const queue = this.getQueueForChannel(channel);
      const pattern = this.getPatternForChannel(channel);

      await queue.emit(pattern, message).toPromise();

      this.logger.log(`Message routed to ${channel} queue: ${message.id}`);
    } catch (error) {
      this.logger.error(`Failed to route message to ${channel} queue: ${error.message}`);
      await this.sendToRetryQueue(message, error.message);
    }
  }

  /**
   * Send message to result queue
   */
  async sendResult(message: any): Promise<void> {
    try {
      await this.resultQueue.emit('notification.result', message).toPromise();
      this.logger.log(`Result sent to queue: ${message.id}`);
    } catch (error) {
      this.logger.error(`Failed to send result to queue: ${error.message}`);
    }
  }

  /**
   * Send message to retry queue
   */
  async sendToRetryQueue(message: NotificationQueueMessage, error: string): Promise<void> {
    try {
      const retryMessage = {
        id: message.id,
        originalMessage: message,
        retryCount: (message.retryCount || 0) + 1,
        lastError: error,
        nextRetryAt: new Date(Date.now() + this.getRetryDelay(message.retryCount || 0)),
        createdAt: new Date(),
      };

      await this.retryQueue.emit('notification.retry', retryMessage).toPromise();
      this.logger.log(`Message sent to retry queue: ${message.id}`);
    } catch (retryError) {
      this.logger.error(`Failed to send to retry queue: ${retryError.message}`);
      await this.sendToDeadLetterQueue(message, error);
    }
  }

  /**
   * Send message to dead letter queue
   */
  async sendToDeadLetterQueue(message: NotificationQueueMessage, error: string): Promise<void> {
    try {
      const dlqMessage = {
        ...message,
        failureReason: error,
        failedAt: new Date(),
      };

      await this.dlqQueue.emit('notification.failed', dlqMessage).toPromise();
      this.logger.error(`Message sent to DLQ: ${message.id} - ${error}`);
    } catch (dlqError) {
      this.logger.error(`Failed to send to DLQ: ${dlqError.message}`);
    }
  }

  /**
   * Get the appropriate queue client for a channel
   */
  private getQueueForChannel(channel: NotificationChannel): ClientProxy {
    switch (channel) {
      case NotificationChannel.Fcm:
        return this.fcmQueue;
      case NotificationChannel.Apns:
        return this.apnsQueue;
      case NotificationChannel.Email:
        return this.emailQueue;
      case NotificationChannel.Sms:
        return this.smsQueue;
      case NotificationChannel.Wns:
        return this.wnsQueue;
      default:
        throw new Error(`Unsupported notification channel: ${channel}`);
    }
  }

  /**
   * Get the message pattern for a channel
   */
  private getPatternForChannel(channel: NotificationChannel): string {
    return `notification.${channel.toLowerCase()}.send`;
  }

  /**
   * Group recipients by their notification channel
   */
  private groupRecipientsByChannel(recipients: any[]): Map<NotificationChannel, any[]> {
    const grouped = new Map<NotificationChannel, any[]>();

    for (const recipient of recipients) {
      const channel = recipient.channel as NotificationChannel;

      if (!grouped.has(channel)) {
        grouped.set(channel, []);
      }

      grouped.get(channel)!.push(recipient);
    }

    return grouped;
  }

  /**
   * Calculate retry delay with exponential backoff
   */
  private getRetryDelay(retryCount: number): number {
    const baseDelay = 1000; // 1 second
    const maxDelay = 300000; // 5 minutes
    const delay = Math.min(baseDelay * Math.pow(2, retryCount), maxDelay);

    // Add jitter to prevent thundering herd
    return delay + Math.random() * 1000;
  }

  /**
   * Get all supported channels
   */
  getSupportedChannels(): NotificationChannel[] {
    return [
      NotificationChannel.Fcm,
      NotificationChannel.Apns,
      NotificationChannel.Email,
      NotificationChannel.Sms,
      NotificationChannel.Wns,
    ];
  }

  /**
   * Check if a channel is supported
   */
  isChannelSupported(channel: string): boolean {
    return this.getSupportedChannels().includes(channel as NotificationChannel);
  }
}
