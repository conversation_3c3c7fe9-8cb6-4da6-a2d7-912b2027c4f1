import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import {
  PrismaNotification,
  NotificationWithResults,
  NotificationWithTemplate,
  NotificationWithAll,
  CreateNotificationInput,
  UpdateNotificationInput,
  NotificationWhereInput,
  NotificationOrderByInput,
} from '../types';

@Injectable()
export class NotificationRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateNotificationInput): Promise<PrismaNotification> {
    return this.prisma.notification.create({
      data,
    });
  }

  async findById(id: string): Promise<PrismaNotification | null> {
    return this.prisma.notification.findUnique({
      where: { id },
    });
  }

  async findByIdWithResults(id: string): Promise<NotificationWithResults | null> {
    return this.prisma.notification.findUnique({
      where: { id },
      include: { results: true },
    });
  }

  async findByIdWithTemplate(id: string): Promise<NotificationWithTemplate | null> {
    return this.prisma.notification.findUnique({
      where: { id },
      include: { template: true },
    });
  }

  async findByIdWithAll(id: string): Promise<NotificationWithAll | null> {
    return this.prisma.notification.findUnique({
      where: { id },
      include: {
        results: true,
        template: true,
      },
    });
  }

  async findMany(
    where?: NotificationWhereInput,
    orderBy?: NotificationOrderByInput,
    skip?: number,
    take?: number
  ): Promise<PrismaNotification[]> {
    return this.prisma.notification.findMany({
      where,
      orderBy,
      skip,
      take,
    });
  }

  async findManyWithResults(
    where?: NotificationWhereInput,
    orderBy?: NotificationOrderByInput,
    skip?: number,
    take?: number
  ): Promise<NotificationWithResults[]> {
    return this.prisma.notification.findMany({
      where,
      orderBy,
      skip,
      take,
      include: { results: true },
    });
  }

  async update(id: string, data: UpdateNotificationInput): Promise<PrismaNotification> {
    return this.prisma.notification.update({
      where: { id },
      data,
    });
  }

  async delete(id: string): Promise<PrismaNotification> {
    return this.prisma.notification.delete({
      where: { id },
    });
  }

  async count(where?: NotificationWhereInput): Promise<number> {
    return this.prisma.notification.count({
      where,
    });
  }

  async findByRecipientId(
    recipientId: string,
    skip?: number,
    take?: number
  ): Promise<PrismaNotification[]> {
    return this.prisma.notification.findMany({
      where: {
        recipients: {
          path: '$[*].id',
          equals: recipientId,
        },
      },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
    });
  }

  async findByChannel(
    channel: string,
    skip?: number,
    take?: number
  ): Promise<PrismaNotification[]> {
    return this.prisma.notification.findMany({
      where: {
        channels: {
          has: channel,
        },
      },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
    });
  }

  async findScheduled(before?: Date): Promise<PrismaNotification[]> {
    return this.prisma.notification.findMany({
      where: {
        scheduledAt: {
          lte: before || new Date(),
        },
      },
      orderBy: { scheduledAt: 'asc' },
    });
  }

  async findExpired(): Promise<PrismaNotification[]> {
    return this.prisma.notification.findMany({
      where: {
        expiresAt: {
          lte: new Date(),
        },
      },
    });
  }
}
