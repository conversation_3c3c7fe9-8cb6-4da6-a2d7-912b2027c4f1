import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import {
  PrismaNotificationResult,
  NotificationResultWithNotification,
  CreateNotificationResultInput,
  UpdateNotificationResultInput,
  NotificationResultWhereInput,
  NotificationResultOrderByInput,
} from '../types';

@Injectable()
export class NotificationResultRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateNotificationResultInput): Promise<PrismaNotificationResult> {
    return this.prisma.notificationResult.create({
      data,
    });
  }

  async createMany(data: CreateNotificationResultInput[]): Promise<{ count: number }> {
    return this.prisma.notificationResult.createMany({
      data,
    });
  }

  async findById(id: string): Promise<PrismaNotificationResult | null> {
    return this.prisma.notificationResult.findUnique({
      where: { id },
    });
  }

  async findByIdWithNotification(id: string): Promise<NotificationResultWithNotification | null> {
    return this.prisma.notificationResult.findUnique({
      where: { id },
      include: { notification: true },
    });
  }

  async findMany(
    where?: NotificationResultWhereInput,
    orderBy?: NotificationResultOrderByInput,
    skip?: number,
    take?: number
  ): Promise<PrismaNotificationResult[]> {
    return this.prisma.notificationResult.findMany({
      where,
      orderBy,
      skip,
      take,
    });
  }

  async findManyWithNotification(
    where?: NotificationResultWhereInput,
    orderBy?: NotificationResultOrderByInput,
    skip?: number,
    take?: number
  ): Promise<NotificationResultWithNotification[]> {
    return this.prisma.notificationResult.findMany({
      where,
      orderBy,
      skip,
      take,
      include: { notification: true },
    });
  }

  async update(id: string, data: UpdateNotificationResultInput): Promise<PrismaNotificationResult> {
    return this.prisma.notificationResult.update({
      where: { id },
      data,
    });
  }

  async updateMany(
    where: NotificationResultWhereInput,
    data: UpdateNotificationResultInput
  ): Promise<{ count: number }> {
    return this.prisma.notificationResult.updateMany({
      where,
      data,
    });
  }

  async delete(id: string): Promise<PrismaNotificationResult> {
    return this.prisma.notificationResult.delete({
      where: { id },
    });
  }

  async count(where?: NotificationResultWhereInput): Promise<number> {
    return this.prisma.notificationResult.count({
      where,
    });
  }

  async findByNotificationId(notificationId: string): Promise<PrismaNotificationResult[]> {
    return this.prisma.notificationResult.findMany({
      where: { notificationId },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findByRecipientId(
    recipientId: string,
    skip?: number,
    take?: number
  ): Promise<PrismaNotificationResult[]> {
    return this.prisma.notificationResult.findMany({
      where: { recipientId },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
    });
  }

  async findByChannel(
    channel: string,
    skip?: number,
    take?: number
  ): Promise<PrismaNotificationResult[]> {
    return this.prisma.notificationResult.findMany({
      where: { channel },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
    });
  }

  async findByStatus(
    status: string,
    skip?: number,
    take?: number
  ): Promise<PrismaNotificationResult[]> {
    return this.prisma.notificationResult.findMany({
      where: { status },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
    });
  }

  async findPendingResults(): Promise<PrismaNotificationResult[]> {
    return this.prisma.notificationResult.findMany({
      where: { status: 'PENDING' },
      orderBy: { createdAt: 'asc' },
    });
  }

  async findFailedResults(): Promise<PrismaNotificationResult[]> {
    return this.prisma.notificationResult.findMany({
      where: { status: 'FAILED' },
      orderBy: { createdAt: 'desc' },
    });
  }

  async getDeliveryStats(notificationId?: string): Promise<{
    total: number;
    sent: number;
    delivered: number;
    failed: number;
    pending: number;
  }> {
    const where = notificationId ? { notificationId } : {};

    const [total, sent, delivered, failed, pending] = await Promise.all([
      this.count(where),
      this.count({ ...where, status: 'SENT' }),
      this.count({ ...where, status: 'DELIVERED' }),
      this.count({ ...where, status: 'FAILED' }),
      this.count({ ...where, status: 'PENDING' }),
    ]);

    return { total, sent, delivered, failed, pending };
  }
}
