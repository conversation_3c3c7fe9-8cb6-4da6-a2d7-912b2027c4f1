import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma.service';
import {
  PrismaNotificationTemplate,
  NotificationTemplateWithNotifications,
  CreateNotificationTemplateInput,
  UpdateNotificationTemplateInput,
  NotificationTemplateWhereInput,
  NotificationTemplateOrderByInput,
} from '../types';

@Injectable()
export class NotificationTemplateRepository {
  constructor(private readonly prisma: PrismaService) {}

  async create(data: CreateNotificationTemplateInput): Promise<PrismaNotificationTemplate> {
    return this.prisma.notificationTemplate.create({
      data,
    });
  }

  async findById(id: string): Promise<PrismaNotificationTemplate | null> {
    return this.prisma.notificationTemplate.findUnique({
      where: { id },
    });
  }

  async findByIdWithNotifications(
    id: string
  ): Promise<NotificationTemplateWithNotifications | null> {
    return this.prisma.notificationTemplate.findUnique({
      where: { id },
      include: { notifications: true },
    });
  }

  async findByName(name: string): Promise<PrismaNotificationTemplate | null> {
    return this.prisma.notificationTemplate.findUnique({
      where: { name },
    });
  }

  async findMany(
    where?: NotificationTemplateWhereInput,
    orderBy?: NotificationTemplateOrderByInput,
    skip?: number,
    take?: number
  ): Promise<PrismaNotificationTemplate[]> {
    return this.prisma.notificationTemplate.findMany({
      where,
      orderBy,
      skip,
      take,
    });
  }

  async findManyWithNotifications(
    where?: NotificationTemplateWhereInput,
    orderBy?: NotificationTemplateOrderByInput,
    skip?: number,
    take?: number
  ): Promise<NotificationTemplateWithNotifications[]> {
    return this.prisma.notificationTemplate.findMany({
      where,
      orderBy,
      skip,
      take,
      include: { notifications: true },
    });
  }

  async update(
    id: string,
    data: UpdateNotificationTemplateInput
  ): Promise<PrismaNotificationTemplate> {
    return this.prisma.notificationTemplate.update({
      where: { id },
      data,
    });
  }

  async delete(id: string): Promise<PrismaNotificationTemplate> {
    return this.prisma.notificationTemplate.delete({
      where: { id },
    });
  }

  async count(where?: NotificationTemplateWhereInput): Promise<number> {
    return this.prisma.notificationTemplate.count({
      where,
    });
  }

  async findActive(skip?: number, take?: number): Promise<PrismaNotificationTemplate[]> {
    return this.prisma.notificationTemplate.findMany({
      where: { isActive: true },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
    });
  }

  async findByChannel(
    channel: string,
    skip?: number,
    take?: number
  ): Promise<PrismaNotificationTemplate[]> {
    return this.prisma.notificationTemplate.findMany({
      where: {
        channels: {
          has: channel,
        },
      },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
    });
  }

  async findByChannels(
    channels: string[],
    skip?: number,
    take?: number
  ): Promise<PrismaNotificationTemplate[]> {
    return this.prisma.notificationTemplate.findMany({
      where: {
        channels: {
          hasSome: channels,
        },
      },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
    });
  }

  async activate(id: string): Promise<PrismaNotificationTemplate> {
    return this.prisma.notificationTemplate.update({
      where: { id },
      data: { isActive: true },
    });
  }

  async deactivate(id: string): Promise<PrismaNotificationTemplate> {
    return this.prisma.notificationTemplate.update({
      where: { id },
      data: { isActive: false },
    });
  }

  async search(query: string, skip?: number, take?: number): Promise<PrismaNotificationTemplate[]> {
    return this.prisma.notificationTemplate.findMany({
      where: {
        OR: [
          { name: { contains: query, mode: 'insensitive' } },
          { description: { contains: query, mode: 'insensitive' } },
        ],
      },
      skip,
      take,
      orderBy: { createdAt: 'desc' },
    });
  }
}
