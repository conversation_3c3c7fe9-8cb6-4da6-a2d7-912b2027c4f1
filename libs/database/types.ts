import { Prisma } from '@prisma/client';

// Export Prisma types for use in the application
export type PrismaNotification = Prisma.NotificationGetPayload<{}>;
export type PrismaNotificationResult = Prisma.NotificationResultGetPayload<{}>;
export type PrismaNotificationTemplate = Prisma.NotificationTemplateGetPayload<{}>;

// Create types with relations
export type NotificationWithResults = Prisma.NotificationGetPayload<{
  include: { results: true };
}>;

export type NotificationWithTemplate = Prisma.NotificationGetPayload<{
  include: { template: true };
}>;

export type NotificationWithAll = Prisma.NotificationGetPayload<{
  include: {
    results: true;
    template: true;
  };
}>;

export type NotificationTemplateWithNotifications = Prisma.NotificationTemplateGetPayload<{
  include: { notifications: true };
}>;

export type NotificationResultWithNotification = Prisma.NotificationResultGetPayload<{
  include: { notification: true };
}>;

// Input types for creating/updating
export type CreateNotificationInput = Prisma.NotificationCreateInput;
export type UpdateNotificationInput = Prisma.NotificationUpdateInput;
export type CreateNotificationResultInput = Prisma.NotificationResultCreateInput;
export type UpdateNotificationResultInput = Prisma.NotificationResultUpdateInput;
export type CreateNotificationTemplateInput = Prisma.NotificationTemplateCreateInput;
export type UpdateNotificationTemplateInput = Prisma.NotificationTemplateUpdateInput;

// Where clauses
export type NotificationWhereInput = Prisma.NotificationWhereInput;
export type NotificationResultWhereInput = Prisma.NotificationResultWhereInput;
export type NotificationTemplateWhereInput = Prisma.NotificationTemplateWhereInput;

// Order by
export type NotificationOrderByInput = Prisma.NotificationOrderByWithRelationInput;
export type NotificationResultOrderByInput = Prisma.NotificationResultOrderByWithRelationInput;
export type NotificationTemplateOrderByInput = Prisma.NotificationTemplateOrderByWithRelationInput;
