/** @internal */
export const OP_LEFT = "Left" as const

/** @internal */
export type OP_LEFT = typeof OP_LEFT

/** @internal */
export const OP_RIGHT = "Right" as const

/** @internal */
export type OP_RIGHT = typeof OP_RIGHT

/** @internal */
export const OP_BOTH = "Both" as const

/** @internal */
export type OP_BOTH = typeof OP_BOTH

/** @internal */
export const OP_EITHER = "Either" as const

/** @internal */
export type OP_EITHER = typeof OP_EITHER
