import { ApiPropertyOptions } from '../decorators';
import { BaseParameterObject, ReferenceObject, SchemaObject } from '../interfaces/open-api-spec.interface';
import { ParamWithTypeMetadata } from './parameter-metadata-accessor';
type KeysToRemove = keyof ApiPropertyOptions | '$ref' | 'properties' | 'enumName' | 'enumSchema' | 'selfRequired';
export declare class SwaggerTypesMapper {
    private readonly keysToRemove;
    mapParamTypes(parameters: Array<ParamWithTypeMetadata | BaseParameterObject>): ({
        schema: {
            type: string;
            items: any;
            nullable?: boolean;
            discriminator?: import("../interfaces/open-api-spec.interface").DiscriminatorObject;
            readOnly?: boolean;
            writeOnly?: boolean;
            xml?: import("../interfaces/open-api-spec.interface").XmlObject;
            externalDocs?: import("../interfaces/open-api-spec.interface").ExternalDocumentationObject;
            example?: any;
            examples?: any[] | Record<string, any>;
            deprecated?: boolean;
            allOf?: (SchemaObject | ReferenceObject)[];
            oneOf?: (SchemaObject | ReferenceObject)[];
            anyOf?: (SchemaObject | ReferenceObject)[];
            not?: SchemaObject | ReferenceObject;
            properties?: Record<string, SchemaObject | ReferenceObject>;
            additionalProperties?: SchemaObject | ReferenceObject | boolean;
            patternProperties?: SchemaObject | ReferenceObject | any;
            description?: string;
            format?: string;
            default?: any;
            title?: string;
            multipleOf?: number;
            maximum?: number;
            exclusiveMaximum?: boolean;
            minimum?: number;
            exclusiveMinimum?: boolean;
            maxLength?: number;
            minLength?: number;
            pattern?: string;
            maxItems?: number;
            minItems?: number;
            uniqueItems?: boolean;
            maxProperties?: number;
            minProperties?: number;
            required?: string[];
            enum?: any[];
            'x-enumNames'?: string[];
        };
    } | {
        schema: import("lodash").Dictionary<any>;
        description?: string;
        required?: boolean;
        deprecated?: boolean;
        allowEmptyValue?: boolean;
        style?: import("../interfaces/open-api-spec.interface").ParameterStyle;
        explode?: boolean;
        allowReserved?: boolean;
        examples?: Record<string, import("../interfaces/open-api-spec.interface").ExampleObject | ReferenceObject>;
        example?: any;
        content?: import("../interfaces/open-api-spec.interface").ContentObject;
    } | {
        schema: import("lodash").Dictionary<any>;
        name?: string | number | object;
        type?: import("@nestjs/common").Type<unknown>;
        in?: import("../interfaces/open-api-spec.interface").ParameterLocation | "body" | "placeholder";
        isArray?: boolean;
        items?: SchemaObject;
        required?: boolean;
        enum?: unknown[];
        enumName?: string;
        enumSchema?: import("../interfaces/enum-schema-attributes.interface").EnumSchemaAttributes;
        selfRequired?: boolean;
    } | Partial<BaseParameterObject> | Partial<ParamWithTypeMetadata>)[];
    mapTypeToOpenAPIType(type: string | Function): string;
    mapEnumArrayType(param: Record<string, any>, keysToRemove: KeysToRemove[]): {
        schema: {
            type: string;
            items: any;
            nullable?: boolean;
            discriminator?: import("../interfaces/open-api-spec.interface").DiscriminatorObject;
            readOnly?: boolean;
            writeOnly?: boolean;
            xml?: import("../interfaces/open-api-spec.interface").XmlObject;
            externalDocs?: import("../interfaces/open-api-spec.interface").ExternalDocumentationObject;
            example?: any;
            examples?: any[] | Record<string, any>;
            deprecated?: boolean;
            allOf?: (SchemaObject | ReferenceObject)[];
            oneOf?: (SchemaObject | ReferenceObject)[];
            anyOf?: (SchemaObject | ReferenceObject)[];
            not?: SchemaObject | ReferenceObject;
            properties?: Record<string, SchemaObject | ReferenceObject>;
            additionalProperties?: SchemaObject | ReferenceObject | boolean;
            patternProperties?: SchemaObject | ReferenceObject | any;
            description?: string;
            format?: string;
            default?: any;
            title?: string;
            multipleOf?: number;
            maximum?: number;
            exclusiveMaximum?: boolean;
            minimum?: number;
            exclusiveMinimum?: boolean;
            maxLength?: number;
            minLength?: number;
            pattern?: string;
            maxItems?: number;
            minItems?: number;
            uniqueItems?: boolean;
            maxProperties?: number;
            minProperties?: number;
            required?: string[];
            enum?: any[];
            'x-enumNames'?: string[];
        };
    };
    mapArrayType(param: (ParamWithTypeMetadata & SchemaObject) | BaseParameterObject, keysToRemove: KeysToRemove[]): {
        schema: {
            type: string;
            items: any;
            nullable?: boolean;
            discriminator?: import("../interfaces/open-api-spec.interface").DiscriminatorObject;
            readOnly?: boolean;
            writeOnly?: boolean;
            xml?: import("../interfaces/open-api-spec.interface").XmlObject;
            externalDocs?: import("../interfaces/open-api-spec.interface").ExternalDocumentationObject;
            example?: any;
            examples?: any[] | Record<string, any>;
            deprecated?: boolean;
            allOf?: (SchemaObject | ReferenceObject)[];
            oneOf?: (SchemaObject | ReferenceObject)[];
            anyOf?: (SchemaObject | ReferenceObject)[];
            not?: SchemaObject | ReferenceObject;
            properties?: Record<string, SchemaObject | ReferenceObject>;
            additionalProperties?: SchemaObject | ReferenceObject | boolean;
            patternProperties?: SchemaObject | ReferenceObject | any;
            description?: string;
            format?: string;
            default?: any;
            title?: string;
            multipleOf?: number;
            maximum?: number;
            exclusiveMaximum?: boolean;
            minimum?: number;
            exclusiveMinimum?: boolean;
            maxLength?: number;
            minLength?: number;
            pattern?: string;
            maxItems?: number;
            minItems?: number;
            uniqueItems?: boolean;
            maxProperties?: number;
            minProperties?: number;
            required?: string[];
            enum?: any[];
            'x-enumNames'?: string[];
        };
        description?: string;
        required?: boolean;
        deprecated?: boolean;
        allowEmptyValue?: boolean;
        style?: import("../interfaces/open-api-spec.interface").ParameterStyle;
        explode?: boolean;
        allowReserved?: boolean;
        examples?: Record<string, import("../interfaces/open-api-spec.interface").ExampleObject | ReferenceObject>;
        example?: any;
        content?: import("../interfaces/open-api-spec.interface").ContentObject;
    } | {
        schema: {
            type: string;
            items: any;
            nullable?: boolean;
            discriminator?: import("../interfaces/open-api-spec.interface").DiscriminatorObject;
            readOnly?: boolean;
            writeOnly?: boolean;
            xml?: import("../interfaces/open-api-spec.interface").XmlObject;
            externalDocs?: import("../interfaces/open-api-spec.interface").ExternalDocumentationObject;
            example?: any;
            examples?: any[] | Record<string, any>;
            deprecated?: boolean;
            allOf?: (SchemaObject | ReferenceObject)[];
            oneOf?: (SchemaObject | ReferenceObject)[];
            anyOf?: (SchemaObject | ReferenceObject)[];
            not?: SchemaObject | ReferenceObject;
            properties?: Record<string, SchemaObject | ReferenceObject>;
            additionalProperties?: SchemaObject | ReferenceObject | boolean;
            patternProperties?: SchemaObject | ReferenceObject | any;
            description?: string;
            format?: string;
            default?: any;
            title?: string;
            multipleOf?: number;
            maximum?: number;
            exclusiveMaximum?: boolean;
            minimum?: number;
            exclusiveMinimum?: boolean;
            maxLength?: number;
            minLength?: number;
            pattern?: string;
            maxItems?: number;
            minItems?: number;
            uniqueItems?: boolean;
            maxProperties?: number;
            minProperties?: number;
            required?: string[];
            enum?: any[];
            'x-enumNames'?: string[];
        };
        name?: string | number | object;
        type?: import("@nestjs/common").Type<unknown> & string;
        in?: import("../interfaces/open-api-spec.interface").ParameterLocation | "body" | "placeholder";
        isArray?: boolean;
        items?: SchemaObject | (SchemaObject & ReferenceObject);
        required?: boolean & string[];
        enum?: unknown[] & any[];
        enumName?: string;
        enumSchema?: import("../interfaces/enum-schema-attributes.interface").EnumSchemaAttributes;
        selfRequired?: boolean;
        nullable?: boolean;
        discriminator?: import("../interfaces/open-api-spec.interface").DiscriminatorObject;
        readOnly?: boolean;
        writeOnly?: boolean;
        xml?: import("../interfaces/open-api-spec.interface").XmlObject;
        externalDocs?: import("../interfaces/open-api-spec.interface").ExternalDocumentationObject;
        example?: any;
        examples?: any[] | Record<string, any>;
        deprecated?: boolean;
        allOf?: (SchemaObject | ReferenceObject)[];
        oneOf?: (SchemaObject | ReferenceObject)[];
        anyOf?: (SchemaObject | ReferenceObject)[];
        not?: SchemaObject | ReferenceObject;
        properties?: Record<string, SchemaObject | ReferenceObject>;
        additionalProperties?: SchemaObject | ReferenceObject | boolean;
        patternProperties?: SchemaObject | ReferenceObject | any;
        description?: string;
        format?: string;
        default?: any;
        title?: string;
        multipleOf?: number;
        maximum?: number;
        exclusiveMaximum?: boolean;
        minimum?: number;
        exclusiveMinimum?: boolean;
        maxLength?: number;
        minLength?: number;
        pattern?: string;
        maxItems?: number;
        minItems?: number;
        uniqueItems?: boolean;
        maxProperties?: number;
        minProperties?: number;
        'x-enumNames'?: string[];
    };
    getSchemaOptionsKeys(): Array<keyof SchemaObject>;
    private getSchemaOptions;
    private isEnumArrayType;
    private hasSchemaDefinition;
    private hasRawContentDefinition;
    private omitParamKeys;
}
export {};
