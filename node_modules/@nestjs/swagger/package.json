{"name": "@nestjs/swagger", "version": "11.2.0", "description": "Nest - modern, fast, powerful node.js web framework (@swagger)", "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "repository": "https://github.com/nestjs/swagger", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p tsconfig.build.json", "format": "prettier \"lib/**/*.ts\" --write", "lint": "eslint 'lib/**/*.ts' --fix", "prepublish:next": "npm run build", "publish:next": "npm publish --access public --tag next", "prepublish:npm": "npm run build", "publish:npm": "npm publish --access public", "prepare": "npm run build", "test": "jest", "test:dev": "jest --watch", "test:e2e": "jest --config e2e/jest-e2e.json", "prerelease": "npm run build", "release": "release-it", "---manual-tests---": "", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --watch --debug"}, "dependencies": {"@microsoft/tsdoc": "0.15.1", "@nestjs/mapped-types": "2.1.0", "js-yaml": "4.1.0", "lodash": "4.17.21", "path-to-regexp": "8.2.0", "swagger-ui-dist": "5.21.0"}, "devDependencies": {"@commitlint/cli": "19.8.0", "@commitlint/config-angular": "19.8.0", "@eslint/eslintrc": "3.3.1", "@eslint/js": "9.26.0", "@fastify/static": "8.1.1", "@nestjs/common": "11.1.0", "@nestjs/core": "11.1.0", "@nestjs/platform-express": "11.1.0", "@nestjs/platform-fastify": "11.1.0", "@types/jest": "29.5.14", "@types/js-yaml": "4.0.9", "@types/lodash": "4.17.16", "@types/node": "22.15.3", "class-transformer": "0.5.1", "class-validator": "0.14.2", "eslint": "9.26.0", "eslint-config-prettier": "10.1.2", "eslint-plugin-prettier": "5.3.1", "express": "5.1.0", "fastify": "5.3.2", "globals": "16.0.0", "husky": "9.1.7", "jest": "29.7.0", "lerna-changelog": "2.2.0", "lint-staged": "15.5.1", "openapi-types": "12.1.3", "prettier": "3.5.3", "prettier-v2": "npm:prettier@2.8.8", "reflect-metadata": "0.2.2", "release-it": "19.0.2", "supertest": "7.1.0", "swagger-parser": "10.0.3", "ts-jest": "29.3.2", "typescript": "5.8.3", "typescript-eslint": "8.31.1"}, "peerDependencies": {"@fastify/static": "^8.0.0", "@nestjs/common": "^11.0.1", "@nestjs/core": "^11.0.1", "class-transformer": "*", "class-validator": "*", "reflect-metadata": "^0.1.12 || ^0.2.0"}, "peerDependenciesMeta": {"@fastify/static": {"optional": true}, "class-transformer": {"optional": true}, "class-validator": {"optional": true}}, "lint-staged": {"*.ts": ["prettier --write", "git add -f"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -c .commitlintrc.json -E HUSKY_GIT_PARAMS"}}, "changelog": {"labels": {"feature": "Features", "bug": "Bug fixes", "enhancement": "Enhancements", "docs": "Docs", "dependencies": "Dependencies", "type: code style": "Code style tweaks", "breaking change": "Breaking changes"}}}