var He=Object.defineProperty;var Ve=e=>{throw TypeError(e)};var je=(e,n)=>{for(var i in n)He(e,i,{get:n[i],enumerable:!0})};var ne=(e,n,i)=>n.has(e)?Ve("Cannot add the same private member more than once"):n instanceof WeakSet?n.add(e):n.set(e,i);var Pe={};je(Pe,{validator:()=>be});function be(...e){return n=>n}var ie=Symbol(),pe=new WeakMap,ge=class{constructor(n){n===ie?pe.set(this,"Prisma.".concat(this._getName())):pe.set(this,"new Prisma.".concat(this._getNamespace(),".").concat(this._getName(),"()"))}_getName(){return this.constructor.name}toString(){return pe.get(this)}},G=class extends ge{_getNamespace(){return"NullTypes"}},me,J=class extends G{constructor(){super(...arguments);ne(this,me)}};me=new WeakMap;ve(J,"DbNull");var we,X=class extends G{constructor(){super(...arguments);ne(this,we)}};we=new WeakMap;ve(X,"JsonNull");var Ne,K=class extends G{constructor(){super(...arguments);ne(this,Ne)}};Ne=new WeakMap;ve(K,"AnyNull");var We={classes:{DbNull:J,JsonNull:X,AnyNull:K},instances:{DbNull:new J(ie),JsonNull:new X(ie),AnyNull:new K(ie)}};function ve(e,n){Object.defineProperty(e,"name",{value:n,configurable:!0})}var Ge=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function Je(e){return new Proxy(e,{get(n,i){if(i in n)return n[i];if(!Ge.has(i))throw new TypeError("Invalid enum value: ".concat(String(i)))}})}var Xe=()=>{var e,n;return((n=(e=globalThis.process)==null?void 0:e.release)==null?void 0:n.name)==="node"},Ke=()=>{var e,n;return!!globalThis.Bun||!!((n=(e=globalThis.process)==null?void 0:e.versions)!=null&&n.bun)},Qe=()=>!!globalThis.Deno,Ye=()=>typeof globalThis.Netlify=="object",xe=()=>typeof globalThis.EdgeRuntime=="object",ze=()=>{var e;return((e=globalThis.navigator)==null?void 0:e.userAgent)==="Cloudflare-Workers"};function ye(){var i;return(i=[[Ye,"netlify"],[xe,"edge-light"],[ze,"workerd"],[Qe,"deno"],[Ke,"bun"],[Xe,"node"]].flatMap(t=>t[0]()?[t[1]]:[]).at(0))!=null?i:""}var en={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function nn(){let e=ye();return{id:e,prettyName:en[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}var V=9e15,H=1e9,Ee="0123456789abcdef",se="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",oe="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",ke={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-V,maxE:V,crypto:!1},qe,Z,w=!0,fe="[DecimalError] ",$=fe+"Invalid argument: ",_e=fe+"Precision limit exceeded",Te=fe+"crypto unavailable",De="[object Decimal]",R=Math.floor,C=Math.pow,tn=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,rn=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,sn=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,Fe=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,D=1e7,m=7,on=9007199254740991,un=se.length-1,Se=oe.length-1,h={toStringTag:De};h.absoluteValue=h.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),p(e)};h.ceil=function(){return p(new this.constructor(this),this.e+1,2)};h.clampedTo=h.clamp=function(e,n){var i,t=this,s=t.constructor;if(e=new s(e),n=new s(n),!e.s||!n.s)return new s(NaN);if(e.gt(n))throw Error($+n);return i=t.cmp(e),i<0?e:t.cmp(n)>0?n:new s(t)};h.comparedTo=h.cmp=function(e){var n,i,t,s,r=this,o=r.d,u=(e=new r.constructor(e)).d,c=r.s,f=e.s;if(!o||!u)return!c||!f?NaN:c!==f?c:o===u?0:!o^c<0?1:-1;if(!o[0]||!u[0])return o[0]?c:u[0]?-f:0;if(c!==f)return c;if(r.e!==e.e)return r.e>e.e^c<0?1:-1;for(t=o.length,s=u.length,n=0,i=t<s?t:s;n<i;++n)if(o[n]!==u[n])return o[n]>u[n]^c<0?1:-1;return t===s?0:t>s^c<0?1:-1};h.cosine=h.cos=function(){var e,n,i=this,t=i.constructor;return i.d?i.d[0]?(e=t.precision,n=t.rounding,t.precision=e+Math.max(i.e,i.sd())+m,t.rounding=1,i=fn(t,Be(t,i)),t.precision=e,t.rounding=n,p(Z==2||Z==3?i.neg():i,e,n,!0)):new t(1):new t(NaN)};h.cubeRoot=h.cbrt=function(){var e,n,i,t,s,r,o,u,c,f,l=this,a=l.constructor;if(!l.isFinite()||l.isZero())return new a(l);for(w=!1,r=l.s*C(l.s*l,1/3),!r||Math.abs(r)==1/0?(i=b(l.d),e=l.e,(r=(e-i.length+1)%3)&&(i+=r==1||r==-2?"0":"00"),r=C(i,1/3),e=R((e+1)/3)-(e%3==(e<0?-1:2)),r==1/0?i="5e"+e:(i=r.toExponential(),i=i.slice(0,i.indexOf("e")+1)+e),t=new a(i),t.s=l.s):t=new a(r.toString()),o=(e=a.precision)+3;;)if(u=t,c=u.times(u).times(u),f=c.plus(l),t=k(f.plus(l).times(u),f.plus(c),o+2,1),b(u.d).slice(0,o)===(i=b(t.d)).slice(0,o))if(i=i.slice(o-3,o+1),i=="9999"||!s&&i=="4999"){if(!s&&(p(u,e+1,0),u.times(u).times(u).eq(l))){t=u;break}o+=4,s=1}else{(!+i||!+i.slice(1)&&i.charAt(0)=="5")&&(p(t,e+1,1),n=!t.times(t).times(t).eq(l));break}return w=!0,p(t,e,a.rounding,n)};h.decimalPlaces=h.dp=function(){var e,n=this.d,i=NaN;if(n){if(e=n.length-1,i=(e-R(this.e/m))*m,e=n[e],e)for(;e%10==0;e/=10)i--;i<0&&(i=0)}return i};h.dividedBy=h.div=function(e){return k(this,new this.constructor(e))};h.dividedToIntegerBy=h.divToInt=function(e){var n=this,i=n.constructor;return p(k(n,new i(e),0,1,1),i.precision,i.rounding)};h.equals=h.eq=function(e){return this.cmp(e)===0};h.floor=function(){return p(new this.constructor(this),this.e+1,3)};h.greaterThan=h.gt=function(e){return this.cmp(e)>0};h.greaterThanOrEqualTo=h.gte=function(e){var n=this.cmp(e);return n==1||n===0};h.hyperbolicCosine=h.cosh=function(){var e,n,i,t,s,r=this,o=r.constructor,u=new o(1);if(!r.isFinite())return new o(r.s?1/0:NaN);if(r.isZero())return u;i=o.precision,t=o.rounding,o.precision=i+Math.max(r.e,r.sd())+4,o.rounding=1,s=r.d.length,s<32?(e=Math.ceil(s/3),n=(1/le(4,e)).toString()):(e=16,n="2.3283064365386962890625e-10"),r=j(o,1,r.times(n),new o(1),!0);for(var c,f=e,l=new o(8);f--;)c=r.times(r),r=u.minus(c.times(l.minus(c.times(l))));return p(r,o.precision=i,o.rounding=t,!0)};h.hyperbolicSine=h.sinh=function(){var e,n,i,t,s=this,r=s.constructor;if(!s.isFinite()||s.isZero())return new r(s);if(n=r.precision,i=r.rounding,r.precision=n+Math.max(s.e,s.sd())+4,r.rounding=1,t=s.d.length,t<3)s=j(r,2,s,s,!0);else{e=1.4*Math.sqrt(t),e=e>16?16:e|0,s=s.times(1/le(5,e)),s=j(r,2,s,s,!0);for(var o,u=new r(5),c=new r(16),f=new r(20);e--;)o=s.times(s),s=s.times(u.plus(o.times(c.times(o).plus(f))))}return r.precision=n,r.rounding=i,p(s,n,i,!0)};h.hyperbolicTangent=h.tanh=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+7,t.rounding=1,k(i.sinh(),i.cosh(),t.precision=e,t.rounding=n)):new t(i.s)};h.inverseCosine=h.acos=function(){var e=this,n=e.constructor,i=e.abs().cmp(1),t=n.precision,s=n.rounding;return i!==-1?i===0?e.isNeg()?F(n,t,s):new n(0):new n(NaN):e.isZero()?F(n,t+4,s).times(.5):(n.precision=t+6,n.rounding=1,e=new n(1).minus(e).div(e.plus(1)).sqrt().atan(),n.precision=t,n.rounding=s,e.times(2))};h.inverseHyperbolicCosine=h.acosh=function(){var e,n,i=this,t=i.constructor;return i.lte(1)?new t(i.eq(1)?0:NaN):i.isFinite()?(e=t.precision,n=t.rounding,t.precision=e+Math.max(Math.abs(i.e),i.sd())+4,t.rounding=1,w=!1,i=i.times(i).minus(1).sqrt().plus(i),w=!0,t.precision=e,t.rounding=n,i.ln()):new t(i)};h.inverseHyperbolicSine=h.asinh=function(){var e,n,i=this,t=i.constructor;return!i.isFinite()||i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+2*Math.max(Math.abs(i.e),i.sd())+6,t.rounding=1,w=!1,i=i.times(i).plus(1).sqrt().plus(i),w=!0,t.precision=e,t.rounding=n,i.ln())};h.inverseHyperbolicTangent=h.atanh=function(){var e,n,i,t,s=this,r=s.constructor;return s.isFinite()?s.e>=0?new r(s.abs().eq(1)?s.s/0:s.isZero()?s:NaN):(e=r.precision,n=r.rounding,t=s.sd(),Math.max(t,e)<2*-s.e-1?p(new r(s),e,n,!0):(r.precision=i=t-s.e,s=k(s.plus(1),new r(1).minus(s),i+e,1),r.precision=e+4,r.rounding=1,s=s.ln(),r.precision=e,r.rounding=n,s.times(.5))):new r(NaN)};h.inverseSine=h.asin=function(){var e,n,i,t,s=this,r=s.constructor;return s.isZero()?new r(s):(n=s.abs().cmp(1),i=r.precision,t=r.rounding,n!==-1?n===0?(e=F(r,i+4,t).times(.5),e.s=s.s,e):new r(NaN):(r.precision=i+6,r.rounding=1,s=s.div(new r(1).minus(s.times(s)).sqrt().plus(1)).atan(),r.precision=i,r.rounding=t,s.times(2)))};h.inverseTangent=h.atan=function(){var e,n,i,t,s,r,o,u,c,f=this,l=f.constructor,a=l.precision,d=l.rounding;if(f.isFinite()){if(f.isZero())return new l(f);if(f.abs().eq(1)&&a+4<=Se)return o=F(l,a+4,d).times(.25),o.s=f.s,o}else{if(!f.s)return new l(NaN);if(a+4<=Se)return o=F(l,a+4,d).times(.5),o.s=f.s,o}for(l.precision=u=a+10,l.rounding=1,i=Math.min(28,u/m+2|0),e=i;e;--e)f=f.div(f.times(f).plus(1).sqrt().plus(1));for(w=!1,n=Math.ceil(u/m),t=1,c=f.times(f),o=new l(f),s=f;e!==-1;)if(s=s.times(c),r=o.minus(s.div(t+=2)),s=s.times(c),o=r.plus(s.div(t+=2)),o.d[n]!==void 0)for(e=n;o.d[e]===r.d[e]&&e--;);return i&&(o=o.times(2<<i-1)),w=!0,p(o,l.precision=a,l.rounding=d,!0)};h.isFinite=function(){return!!this.d};h.isInteger=h.isInt=function(){return!!this.d&&R(this.e/m)>this.d.length-2};h.isNaN=function(){return!this.s};h.isNegative=h.isNeg=function(){return this.s<0};h.isPositive=h.isPos=function(){return this.s>0};h.isZero=function(){return!!this.d&&this.d[0]===0};h.lessThan=h.lt=function(e){return this.cmp(e)<0};h.lessThanOrEqualTo=h.lte=function(e){return this.cmp(e)<1};h.logarithm=h.log=function(e){var n,i,t,s,r,o,u,c,f=this,l=f.constructor,a=l.precision,d=l.rounding,g=5;if(e==null)e=new l(10),n=!0;else{if(e=new l(e),i=e.d,e.s<0||!i||!i[0]||e.eq(1))return new l(NaN);n=e.eq(10)}if(i=f.d,f.s<0||!i||!i[0]||f.eq(1))return new l(i&&!i[0]?-1/0:f.s!=1?NaN:i?0:1/0);if(n)if(i.length>1)r=!0;else{for(s=i[0];s%10===0;)s/=10;r=s!==1}if(w=!1,u=a+g,o=B(f,u),t=n?ue(l,u+10):B(e,u),c=k(o,t,u,1),Q(c.d,s=a,d))do if(u+=10,o=B(f,u),t=n?ue(l,u+10):B(e,u),c=k(o,t,u,1),!r){+b(c.d).slice(s+1,s+15)+1==1e14&&(c=p(c,a+1,0));break}while(Q(c.d,s+=10,d));return w=!0,p(c,a,d)};h.minus=h.sub=function(e){var n,i,t,s,r,o,u,c,f,l,a,d,g=this,v=g.constructor;if(e=new v(e),!g.d||!e.d)return!g.s||!e.s?e=new v(NaN):g.d?e.s=-e.s:e=new v(e.d||g.s!==e.s?g:NaN),e;if(g.s!=e.s)return e.s=-e.s,g.plus(e);if(f=g.d,d=e.d,u=v.precision,c=v.rounding,!f[0]||!d[0]){if(d[0])e.s=-e.s;else if(f[0])e=new v(g);else return new v(c===3?-0:0);return w?p(e,u,c):e}if(i=R(e.e/m),l=R(g.e/m),f=f.slice(),r=l-i,r){for(a=r<0,a?(n=f,r=-r,o=d.length):(n=d,i=l,o=f.length),t=Math.max(Math.ceil(u/m),o)+2,r>t&&(r=t,n.length=1),n.reverse(),t=r;t--;)n.push(0);n.reverse()}else{for(t=f.length,o=d.length,a=t<o,a&&(o=t),t=0;t<o;t++)if(f[t]!=d[t]){a=f[t]<d[t];break}r=0}for(a&&(n=f,f=d,d=n,e.s=-e.s),o=f.length,t=d.length-o;t>0;--t)f[o++]=0;for(t=d.length;t>r;){if(f[--t]<d[t]){for(s=t;s&&f[--s]===0;)f[s]=D-1;--f[s],f[t]+=D}f[t]-=d[t]}for(;f[--o]===0;)f.pop();for(;f[0]===0;f.shift())--i;return f[0]?(e.d=f,e.e=ce(f,i),w?p(e,u,c):e):new v(c===3?-0:0)};h.modulo=h.mod=function(e){var n,i=this,t=i.constructor;return e=new t(e),!i.d||!e.s||e.d&&!e.d[0]?new t(NaN):!e.d||i.d&&!i.d[0]?p(new t(i),t.precision,t.rounding):(w=!1,t.modulo==9?(n=k(i,e.abs(),0,3,1),n.s*=e.s):n=k(i,e,0,t.modulo,1),n=n.times(e),w=!0,i.minus(n))};h.naturalExponential=h.exp=function(){return Me(this)};h.naturalLogarithm=h.ln=function(){return B(this)};h.negated=h.neg=function(){var e=new this.constructor(this);return e.s=-e.s,p(e)};h.plus=h.add=function(e){var n,i,t,s,r,o,u,c,f,l,a=this,d=a.constructor;if(e=new d(e),!a.d||!e.d)return!a.s||!e.s?e=new d(NaN):a.d||(e=new d(e.d||a.s===e.s?a:NaN)),e;if(a.s!=e.s)return e.s=-e.s,a.minus(e);if(f=a.d,l=e.d,u=d.precision,c=d.rounding,!f[0]||!l[0])return l[0]||(e=new d(a)),w?p(e,u,c):e;if(r=R(a.e/m),t=R(e.e/m),f=f.slice(),s=r-t,s){for(s<0?(i=f,s=-s,o=l.length):(i=l,t=r,o=f.length),r=Math.ceil(u/m),o=r>o?r+1:o+1,s>o&&(s=o,i.length=1),i.reverse();s--;)i.push(0);i.reverse()}for(o=f.length,s=l.length,o-s<0&&(s=o,i=l,l=f,f=i),n=0;s;)n=(f[--s]=f[s]+l[s]+n)/D|0,f[s]%=D;for(n&&(f.unshift(n),++t),o=f.length;f[--o]==0;)f.pop();return e.d=f,e.e=ce(f,t),w?p(e,u,c):e};h.precision=h.sd=function(e){var n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error($+e);return i.d?(n=Le(i.d),e&&i.e+1>n&&(n=i.e+1)):n=NaN,n};h.round=function(){var e=this,n=e.constructor;return p(new n(e),e.e+1,n.rounding)};h.sine=h.sin=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+Math.max(i.e,i.sd())+m,t.rounding=1,i=ln(t,Be(t,i)),t.precision=e,t.rounding=n,p(Z>2?i.neg():i,e,n,!0)):new t(NaN)};h.squareRoot=h.sqrt=function(){var e,n,i,t,s,r,o=this,u=o.d,c=o.e,f=o.s,l=o.constructor;if(f!==1||!u||!u[0])return new l(!f||f<0&&(!u||u[0])?NaN:u?o:1/0);for(w=!1,f=Math.sqrt(+o),f==0||f==1/0?(n=b(u),(n.length+c)%2==0&&(n+="0"),f=Math.sqrt(n),c=R((c+1)/2)-(c<0||c%2),f==1/0?n="5e"+c:(n=f.toExponential(),n=n.slice(0,n.indexOf("e")+1)+c),t=new l(n)):t=new l(f.toString()),i=(c=l.precision)+3;;)if(r=t,t=r.plus(k(o,r,i+2,1)).times(.5),b(r.d).slice(0,i)===(n=b(t.d)).slice(0,i))if(n=n.slice(i-3,i+1),n=="9999"||!s&&n=="4999"){if(!s&&(p(r,c+1,0),r.times(r).eq(o))){t=r;break}i+=4,s=1}else{(!+n||!+n.slice(1)&&n.charAt(0)=="5")&&(p(t,c+1,1),e=!t.times(t).eq(o));break}return w=!0,p(t,c,l.rounding,e)};h.tangent=h.tan=function(){var e,n,i=this,t=i.constructor;return i.isFinite()?i.isZero()?new t(i):(e=t.precision,n=t.rounding,t.precision=e+10,t.rounding=1,i=i.sin(),i.s=1,i=k(i,new t(1).minus(i.times(i)).sqrt(),e+10,0),t.precision=e,t.rounding=n,p(Z==2||Z==4?i.neg():i,e,n,!0)):new t(NaN)};h.times=h.mul=function(e){var n,i,t,s,r,o,u,c,f,l=this,a=l.constructor,d=l.d,g=(e=new a(e)).d;if(e.s*=l.s,!d||!d[0]||!g||!g[0])return new a(!e.s||d&&!d[0]&&!g||g&&!g[0]&&!d?NaN:!d||!g?e.s/0:e.s*0);for(i=R(l.e/m)+R(e.e/m),c=d.length,f=g.length,c<f&&(r=d,d=g,g=r,o=c,c=f,f=o),r=[],o=c+f,t=o;t--;)r.push(0);for(t=f;--t>=0;){for(n=0,s=c+t;s>t;)u=r[s]+g[t]*d[s-t-1]+n,r[s--]=u%D|0,n=u/D|0;r[s]=(r[s]+n)%D|0}for(;!r[--o];)r.pop();return n?++i:r.shift(),e.d=r,e.e=ce(r,i),w?p(e,a.precision,a.rounding):e};h.toBinary=function(e,n){return Ce(this,2,e,n)};h.toDecimalPlaces=h.toDP=function(e,n){var i=this,t=i.constructor;return i=new t(i),e===void 0?i:(q(e,0,H),n===void 0?n=t.rounding:q(n,0,8),p(i,e+i.e+1,n))};h.toExponential=function(e,n){var i,t=this,s=t.constructor;return e===void 0?i=L(t,!0):(q(e,0,H),n===void 0?n=s.rounding:q(n,0,8),t=p(new s(t),e+1,n),i=L(t,!0,e+1)),t.isNeg()&&!t.isZero()?"-"+i:i};h.toFixed=function(e,n){var i,t,s=this,r=s.constructor;return e===void 0?i=L(s):(q(e,0,H),n===void 0?n=r.rounding:q(n,0,8),t=p(new r(s),e+s.e+1,n),i=L(t,!1,e+t.e+1)),s.isNeg()&&!s.isZero()?"-"+i:i};h.toFraction=function(e){var n,i,t,s,r,o,u,c,f,l,a,d,g=this,v=g.d,N=g.constructor;if(!v)return new N(g);if(f=i=new N(1),t=c=new N(0),n=new N(t),r=n.e=Le(v)-g.e-1,o=r%m,n.d[0]=C(10,o<0?m+o:o),e==null)e=r>0?n:f;else{if(u=new N(e),!u.isInt()||u.lt(f))throw Error($+u);e=u.gt(n)?r>0?n:f:u}for(w=!1,u=new N(b(v)),l=N.precision,N.precision=r=v.length*m*2;a=k(u,n,0,1,1),s=i.plus(a.times(t)),s.cmp(e)!=1;)i=t,t=s,s=f,f=c.plus(a.times(s)),c=s,s=n,n=u.minus(a.times(s)),u=s;return s=k(e.minus(i),t,0,1,1),c=c.plus(s.times(f)),i=i.plus(s.times(t)),c.s=f.s=g.s,d=k(f,t,r,1).minus(g).abs().cmp(k(c,i,r,1).minus(g).abs())<1?[f,t]:[c,i],N.precision=l,w=!0,d};h.toHexadecimal=h.toHex=function(e,n){return Ce(this,16,e,n)};h.toNearest=function(e,n){var i=this,t=i.constructor;if(i=new t(i),e==null){if(!i.d)return i;e=new t(1),n=t.rounding}else{if(e=new t(e),n===void 0?n=t.rounding:q(n,0,8),!i.d)return e.s?i:e;if(!e.d)return e.s&&(e.s=i.s),e}return e.d[0]?(w=!1,i=k(i,e,0,n,1).times(e),w=!0,p(i)):(e.s=i.s,i=e),i};h.toNumber=function(){return+this};h.toOctal=function(e,n){return Ce(this,8,e,n)};h.toPower=h.pow=function(e){var n,i,t,s,r,o,u=this,c=u.constructor,f=+(e=new c(e));if(!u.d||!e.d||!u.d[0]||!e.d[0])return new c(C(+u,f));if(u=new c(u),u.eq(1))return u;if(t=c.precision,r=c.rounding,e.eq(1))return p(u,t,r);if(n=R(e.e/m),n>=e.d.length-1&&(i=f<0?-f:f)<=on)return s=Ie(c,u,i,t),e.s<0?new c(1).div(s):p(s,t,r);if(o=u.s,o<0){if(n<e.d.length-1)return new c(NaN);if((e.d[n]&1)==0&&(o=1),u.e==0&&u.d[0]==1&&u.d.length==1)return u.s=o,u}return i=C(+u,f),n=i==0||!isFinite(i)?R(f*(Math.log("0."+b(u.d))/Math.LN10+u.e+1)):new c(i+"").e,n>c.maxE+1||n<c.minE-1?new c(n>0?o/0:0):(w=!1,c.rounding=u.s=1,i=Math.min(12,(n+"").length),s=Me(e.times(B(u,t+i)),t),s.d&&(s=p(s,t+5,1),Q(s.d,t,r)&&(n=t+10,s=p(Me(e.times(B(u,n+i)),n),n+5,1),+b(s.d).slice(t+1,t+15)+1==1e14&&(s=p(s,t+1,0)))),s.s=o,w=!0,c.rounding=r,p(s,t,r))};h.toPrecision=function(e,n){var i,t=this,s=t.constructor;return e===void 0?i=L(t,t.e<=s.toExpNeg||t.e>=s.toExpPos):(q(e,1,H),n===void 0?n=s.rounding:q(n,0,8),t=p(new s(t),e,n),i=L(t,e<=t.e||t.e<=s.toExpNeg,e)),t.isNeg()&&!t.isZero()?"-"+i:i};h.toSignificantDigits=h.toSD=function(e,n){var i=this,t=i.constructor;return e===void 0?(e=t.precision,n=t.rounding):(q(e,1,H),n===void 0?n=t.rounding:q(n,0,8)),p(new t(i),e,n)};h.toString=function(){var e=this,n=e.constructor,i=L(e,e.e<=n.toExpNeg||e.e>=n.toExpPos);return e.isNeg()&&!e.isZero()?"-"+i:i};h.truncated=h.trunc=function(){return p(new this.constructor(this),this.e+1,1)};h.valueOf=h.toJSON=function(){var e=this,n=e.constructor,i=L(e,e.e<=n.toExpNeg||e.e>=n.toExpPos);return e.isNeg()?"-"+i:i};function b(e){var n,i,t,s=e.length-1,r="",o=e[0];if(s>0){for(r+=o,n=1;n<s;n++)t=e[n]+"",i=m-t.length,i&&(r+=U(i)),r+=t;o=e[n],t=o+"",i=m-t.length,i&&(r+=U(i))}else if(o===0)return"0";for(;o%10===0;)o/=10;return r+o}function q(e,n,i){if(e!==~~e||e<n||e>i)throw Error($+e)}function Q(e,n,i,t){var s,r,o,u;for(r=e[0];r>=10;r/=10)--n;return--n<0?(n+=m,s=0):(s=Math.ceil((n+1)/m),n%=m),r=C(10,m-n),u=e[s]%r|0,t==null?n<3?(n==0?u=u/100|0:n==1&&(u=u/10|0),o=i<4&&u==99999||i>3&&u==49999||u==5e4||u==0):o=(i<4&&u+1==r||i>3&&u+1==r/2)&&(e[s+1]/r/100|0)==C(10,n-2)-1||(u==r/2||u==0)&&(e[s+1]/r/100|0)==0:n<4?(n==0?u=u/1e3|0:n==1?u=u/100|0:n==2&&(u=u/10|0),o=(t||i<4)&&u==9999||!t&&i>3&&u==4999):o=((t||i<4)&&u+1==r||!t&&i>3&&u+1==r/2)&&(e[s+1]/r/1e3|0)==C(10,n-3)-1,o}function te(e,n,i){for(var t,s=[0],r,o=0,u=e.length;o<u;){for(r=s.length;r--;)s[r]*=n;for(s[0]+=Ee.indexOf(e.charAt(o++)),t=0;t<s.length;t++)s[t]>i-1&&(s[t+1]===void 0&&(s[t+1]=0),s[t+1]+=s[t]/i|0,s[t]%=i)}return s.reverse()}function fn(e,n){var i,t,s;if(n.isZero())return n;t=n.d.length,t<32?(i=Math.ceil(t/3),s=(1/le(4,i)).toString()):(i=16,s="2.3283064365386962890625e-10"),e.precision+=i,n=j(e,1,n.times(s),new e(1));for(var r=i;r--;){var o=n.times(n);n=o.times(o).minus(o).times(8).plus(1)}return e.precision-=i,n}var k=function(){function e(t,s,r){var o,u=0,c=t.length;for(t=t.slice();c--;)o=t[c]*s+u,t[c]=o%r|0,u=o/r|0;return u&&t.unshift(u),t}function n(t,s,r,o){var u,c;if(r!=o)c=r>o?1:-1;else for(u=c=0;u<r;u++)if(t[u]!=s[u]){c=t[u]>s[u]?1:-1;break}return c}function i(t,s,r,o){for(var u=0;r--;)t[r]-=u,u=t[r]<s[r]?1:0,t[r]=u*o+t[r]-s[r];for(;!t[0]&&t.length>1;)t.shift()}return function(t,s,r,o,u,c){var f,l,a,d,g,v,N,A,M,_,E,P,x,I,ae,z,W,de,T,y,ee=t.constructor,he=t.s==s.s?1:-1,O=t.d,S=s.d;if(!O||!O[0]||!S||!S[0])return new ee(!t.s||!s.s||(O?S&&O[0]==S[0]:!S)?NaN:O&&O[0]==0||!S?he*0:he/0);for(c?(g=1,l=t.e-s.e):(c=D,g=m,l=R(t.e/g)-R(s.e/g)),T=S.length,W=O.length,M=new ee(he),_=M.d=[],a=0;S[a]==(O[a]||0);a++);if(S[a]>(O[a]||0)&&l--,r==null?(I=r=ee.precision,o=ee.rounding):u?I=r+(t.e-s.e)+1:I=r,I<0)_.push(1),v=!0;else{if(I=I/g+2|0,a=0,T==1){for(d=0,S=S[0],I++;(a<W||d)&&I--;a++)ae=d*c+(O[a]||0),_[a]=ae/S|0,d=ae%S|0;v=d||a<W}else{for(d=c/(S[0]+1)|0,d>1&&(S=e(S,d,c),O=e(O,d,c),T=S.length,W=O.length),z=T,E=O.slice(0,T),P=E.length;P<T;)E[P++]=0;y=S.slice(),y.unshift(0),de=S[0],S[1]>=c/2&&++de;do d=0,f=n(S,E,T,P),f<0?(x=E[0],T!=P&&(x=x*c+(E[1]||0)),d=x/de|0,d>1?(d>=c&&(d=c-1),N=e(S,d,c),A=N.length,P=E.length,f=n(N,E,A,P),f==1&&(d--,i(N,T<A?y:S,A,c))):(d==0&&(f=d=1),N=S.slice()),A=N.length,A<P&&N.unshift(0),i(E,N,P,c),f==-1&&(P=E.length,f=n(S,E,T,P),f<1&&(d++,i(E,T<P?y:S,P,c))),P=E.length):f===0&&(d++,E=[0]),_[a++]=d,f&&E[0]?E[P++]=O[z]||0:(E=[O[z]],P=1);while((z++<W||E[0]!==void 0)&&I--);v=E[0]!==void 0}_[0]||_.shift()}if(g==1)M.e=l,qe=v;else{for(a=1,d=_[0];d>=10;d/=10)a++;M.e=a+l*g-1,p(M,u?r+M.e+1:r,o,v)}return M}}();function p(e,n,i,t){var s,r,o,u,c,f,l,a,d,g=e.constructor;e:if(n!=null){if(a=e.d,!a)return e;for(s=1,u=a[0];u>=10;u/=10)s++;if(r=n-s,r<0)r+=m,o=n,l=a[d=0],c=l/C(10,s-o-1)%10|0;else if(d=Math.ceil((r+1)/m),u=a.length,d>=u)if(t){for(;u++<=d;)a.push(0);l=c=0,s=1,r%=m,o=r-m+1}else break e;else{for(l=u=a[d],s=1;u>=10;u/=10)s++;r%=m,o=r-m+s,c=o<0?0:l/C(10,s-o-1)%10|0}if(t=t||n<0||a[d+1]!==void 0||(o<0?l:l%C(10,s-o-1)),f=i<4?(c||t)&&(i==0||i==(e.s<0?3:2)):c>5||c==5&&(i==4||t||i==6&&(r>0?o>0?l/C(10,s-o):0:a[d-1])%10&1||i==(e.s<0?8:7)),n<1||!a[0])return a.length=0,f?(n-=e.e+1,a[0]=C(10,(m-n%m)%m),e.e=-n||0):a[0]=e.e=0,e;if(r==0?(a.length=d,u=1,d--):(a.length=d+1,u=C(10,m-r),a[d]=o>0?(l/C(10,s-o)%C(10,o)|0)*u:0),f)for(;;)if(d==0){for(r=1,o=a[0];o>=10;o/=10)r++;for(o=a[0]+=u,u=1;o>=10;o/=10)u++;r!=u&&(e.e++,a[0]==D&&(a[0]=1));break}else{if(a[d]+=u,a[d]!=D)break;a[d--]=0,u=1}for(r=a.length;a[--r]===0;)a.pop()}return w&&(e.e>g.maxE?(e.d=null,e.e=NaN):e.e<g.minE&&(e.e=0,e.d=[0])),e}function L(e,n,i){if(!e.isFinite())return Ue(e);var t,s=e.e,r=b(e.d),o=r.length;return n?(i&&(t=i-o)>0?r=r.charAt(0)+"."+r.slice(1)+U(t):o>1&&(r=r.charAt(0)+"."+r.slice(1)),r=r+(e.e<0?"e":"e+")+e.e):s<0?(r="0."+U(-s-1)+r,i&&(t=i-o)>0&&(r+=U(t))):s>=o?(r+=U(s+1-o),i&&(t=i-s-1)>0&&(r=r+"."+U(t))):((t=s+1)<o&&(r=r.slice(0,t)+"."+r.slice(t)),i&&(t=i-o)>0&&(s+1===o&&(r+="."),r+=U(t))),r}function ce(e,n){var i=e[0];for(n*=m;i>=10;i/=10)n++;return n}function ue(e,n,i){if(n>un)throw w=!0,i&&(e.precision=i),Error(_e);return p(new e(se),n,1,!0)}function F(e,n,i){if(n>Se)throw Error(_e);return p(new e(oe),n,i,!0)}function Le(e){var n=e.length-1,i=n*m+1;if(n=e[n],n){for(;n%10==0;n/=10)i--;for(n=e[0];n>=10;n/=10)i++}return i}function U(e){for(var n="";e--;)n+="0";return n}function Ie(e,n,i,t){var s,r=new e(1),o=Math.ceil(t/m+4);for(w=!1;;){if(i%2&&(r=r.times(n),Re(r.d,o)&&(s=!0)),i=R(i/2),i===0){i=r.d.length-1,s&&r.d[i]===0&&++r.d[i];break}n=n.times(n),Re(n.d,o)}return w=!0,r}function Oe(e){return e.d[e.d.length-1]&1}function Ze(e,n,i){for(var t,s,r=new e(n[0]),o=0;++o<n.length;){if(s=new e(n[o]),!s.s){r=s;break}t=r.cmp(s),(t===i||t===0&&r.s===i)&&(r=s)}return r}function Me(e,n){var i,t,s,r,o,u,c,f=0,l=0,a=0,d=e.constructor,g=d.rounding,v=d.precision;if(!e.d||!e.d[0]||e.e>17)return new d(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(n==null?(w=!1,c=v):c=n,u=new d(.03125);e.e>-2;)e=e.times(u),a+=5;for(t=Math.log(C(2,a))/Math.LN10*2+5|0,c+=t,i=r=o=new d(1),d.precision=c;;){if(r=p(r.times(e),c,1),i=i.times(++l),u=o.plus(k(r,i,c,1)),b(u.d).slice(0,c)===b(o.d).slice(0,c)){for(s=a;s--;)o=p(o.times(o),c,1);if(n==null)if(f<3&&Q(o.d,c-t,g,f))d.precision=c+=10,i=r=u=new d(1),l=0,f++;else return p(o,d.precision=v,g,w=!0);else return d.precision=v,o}o=u}}function B(e,n){var i,t,s,r,o,u,c,f,l,a,d,g=1,v=10,N=e,A=N.d,M=N.constructor,_=M.rounding,E=M.precision;if(N.s<0||!A||!A[0]||!N.e&&A[0]==1&&A.length==1)return new M(A&&!A[0]?-1/0:N.s!=1?NaN:A?0:N);if(n==null?(w=!1,l=E):l=n,M.precision=l+=v,i=b(A),t=i.charAt(0),Math.abs(r=N.e)<15e14){for(;t<7&&t!=1||t==1&&i.charAt(1)>3;)N=N.times(e),i=b(N.d),t=i.charAt(0),g++;r=N.e,t>1?(N=new M("0."+i),r++):N=new M(t+"."+i.slice(1))}else return f=ue(M,l+2,E).times(r+""),N=B(new M(t+"."+i.slice(1)),l-v).plus(f),M.precision=E,n==null?p(N,E,_,w=!0):N;for(a=N,c=o=N=k(N.minus(1),N.plus(1),l,1),d=p(N.times(N),l,1),s=3;;){if(o=p(o.times(d),l,1),f=c.plus(k(o,new M(s),l,1)),b(f.d).slice(0,l)===b(c.d).slice(0,l))if(c=c.times(2),r!==0&&(c=c.plus(ue(M,l+2,E).times(r+""))),c=k(c,new M(g),l,1),n==null)if(Q(c.d,l-v,_,u))M.precision=l+=v,f=o=N=k(a.minus(1),a.plus(1),l,1),d=p(N.times(N),l,1),s=u=1;else return p(c,M.precision=E,_,w=!0);else return M.precision=E,c;c=f,s+=2}}function Ue(e){return String(e.s*e.s/0)}function re(e,n){var i,t,s;for((i=n.indexOf("."))>-1&&(n=n.replace(".","")),(t=n.search(/e/i))>0?(i<0&&(i=t),i+=+n.slice(t+1),n=n.substring(0,t)):i<0&&(i=n.length),t=0;n.charCodeAt(t)===48;t++);for(s=n.length;n.charCodeAt(s-1)===48;--s);if(n=n.slice(t,s),n){if(s-=t,e.e=i=i-t-1,e.d=[],t=(i+1)%m,i<0&&(t+=m),t<s){for(t&&e.d.push(+n.slice(0,t)),s-=m;t<s;)e.d.push(+n.slice(t,t+=m));n=n.slice(t),t=m-n.length}else t-=s;for(;t--;)n+="0";e.d.push(+n),w&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function cn(e,n){var i,t,s,r,o,u,c,f,l;if(n.indexOf("_")>-1){if(n=n.replace(/(\d)_(?=\d)/g,"$1"),Fe.test(n))return re(e,n)}else if(n==="Infinity"||n==="NaN")return+n||(e.s=NaN),e.e=NaN,e.d=null,e;if(rn.test(n))i=16,n=n.toLowerCase();else if(tn.test(n))i=2;else if(sn.test(n))i=8;else throw Error($+n);for(r=n.search(/p/i),r>0?(c=+n.slice(r+1),n=n.substring(2,r)):n=n.slice(2),r=n.indexOf("."),o=r>=0,t=e.constructor,o&&(n=n.replace(".",""),u=n.length,r=u-r,s=Ie(t,new t(i),r,r*2)),f=te(n,i,D),l=f.length-1,r=l;f[r]===0;--r)f.pop();return r<0?new t(e.s*0):(e.e=ce(f,l),e.d=f,w=!1,o&&(e=k(e,s,u*4)),c&&(e=e.times(Math.abs(c)<54?C(2,c):Y.pow(2,c))),w=!0,e)}function ln(e,n){var i,t=n.d.length;if(t<3)return n.isZero()?n:j(e,2,n,n);i=1.4*Math.sqrt(t),i=i>16?16:i|0,n=n.times(1/le(5,i)),n=j(e,2,n,n);for(var s,r=new e(5),o=new e(16),u=new e(20);i--;)s=n.times(n),n=n.times(r.plus(s.times(o.times(s).minus(u))));return n}function j(e,n,i,t,s){var r,o,u,c,f=1,l=e.precision,a=Math.ceil(l/m);for(w=!1,c=i.times(i),u=new e(t);;){if(o=k(u.times(c),new e(n++*n++),l,1),u=s?t.plus(o):t.minus(o),t=k(o.times(c),new e(n++*n++),l,1),o=u.plus(t),o.d[a]!==void 0){for(r=a;o.d[r]===u.d[r]&&r--;);if(r==-1)break}r=u,u=t,t=o,o=r,f++}return w=!0,o.d.length=a+1,o}function le(e,n){for(var i=e;--n;)i*=e;return i}function Be(e,n){var i,t=n.s<0,s=F(e,e.precision,1),r=s.times(.5);if(n=n.abs(),n.lte(r))return Z=t?4:1,n;if(i=n.divToInt(s),i.isZero())Z=t?3:2;else{if(n=n.minus(i.times(s)),n.lte(r))return Z=Oe(i)?t?2:3:t?4:1,n;Z=Oe(i)?t?1:4:t?3:2}return n.minus(s).abs()}function Ce(e,n,i,t){var s,r,o,u,c,f,l,a,d,g=e.constructor,v=i!==void 0;if(v?(q(i,1,H),t===void 0?t=g.rounding:q(t,0,8)):(i=g.precision,t=g.rounding),!e.isFinite())l=Ue(e);else{for(l=L(e),o=l.indexOf("."),v?(s=2,n==16?i=i*4-3:n==8&&(i=i*3-2)):s=n,o>=0&&(l=l.replace(".",""),d=new g(1),d.e=l.length-o,d.d=te(L(d),10,s),d.e=d.d.length),a=te(l,10,s),r=c=a.length;a[--c]==0;)a.pop();if(!a[0])l=v?"0p+0":"0";else{if(o<0?r--:(e=new g(e),e.d=a,e.e=r,e=k(e,d,i,t,0,s),a=e.d,r=e.e,f=qe),o=a[i],u=s/2,f=f||a[i+1]!==void 0,f=t<4?(o!==void 0||f)&&(t===0||t===(e.s<0?3:2)):o>u||o===u&&(t===4||f||t===6&&a[i-1]&1||t===(e.s<0?8:7)),a.length=i,f)for(;++a[--i]>s-1;)a[i]=0,i||(++r,a.unshift(1));for(c=a.length;!a[c-1];--c);for(o=0,l="";o<c;o++)l+=Ee.charAt(a[o]);if(v){if(c>1)if(n==16||n==8){for(o=n==16?4:3,--c;c%o;c++)l+="0";for(a=te(l,s,n),c=a.length;!a[c-1];--c);for(o=1,l="1.";o<c;o++)l+=Ee.charAt(a[o])}else l=l.charAt(0)+"."+l.slice(1);l=l+(r<0?"p":"p+")+r}else if(r<0){for(;++r;)l="0"+l;l="0."+l}else if(++r>c)for(r-=c;r--;)l+="0";else r<c&&(l=l.slice(0,r)+"."+l.slice(r))}l=(n==16?"0x":n==2?"0b":n==8?"0o":"")+l}return e.s<0?"-"+l:l}function Re(e,n){if(e.length>n)return e.length=n,!0}function an(e){return new this(e).abs()}function dn(e){return new this(e).acos()}function hn(e){return new this(e).acosh()}function pn(e,n){return new this(e).plus(n)}function gn(e){return new this(e).asin()}function mn(e){return new this(e).asinh()}function wn(e){return new this(e).atan()}function Nn(e){return new this(e).atanh()}function vn(e,n){e=new this(e),n=new this(n);var i,t=this.precision,s=this.rounding,r=t+4;return!e.s||!n.s?i=new this(NaN):!e.d&&!n.d?(i=F(this,r,1).times(n.s>0?.25:.75),i.s=e.s):!n.d||e.isZero()?(i=n.s<0?F(this,t,s):new this(0),i.s=e.s):!e.d||n.isZero()?(i=F(this,r,1).times(.5),i.s=e.s):n.s<0?(this.precision=r,this.rounding=1,i=this.atan(k(e,n,r,1)),n=F(this,r,1),this.precision=t,this.rounding=s,i=e.s<0?i.minus(n):i.plus(n)):i=this.atan(k(e,n,r,1)),i}function En(e){return new this(e).cbrt()}function kn(e){return p(e=new this(e),e.e+1,2)}function Sn(e,n,i){return new this(e).clamp(n,i)}function Mn(e){if(!e||typeof e!="object")throw Error(fe+"Object expected");var n,i,t,s=e.defaults===!0,r=["precision",1,H,"rounding",0,8,"toExpNeg",-V,0,"toExpPos",0,V,"maxE",0,V,"minE",-V,0,"modulo",0,9];for(n=0;n<r.length;n+=3)if(i=r[n],s&&(this[i]=ke[i]),(t=e[i])!==void 0)if(R(t)===t&&t>=r[n+1]&&t<=r[n+2])this[i]=t;else throw Error($+i+": "+t);if(i="crypto",s&&(this[i]=ke[i]),(t=e[i])!==void 0)if(t===!0||t===!1||t===0||t===1)if(t)if(typeof crypto<"u"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[i]=!0;else throw Error(Te);else this[i]=!1;else throw Error($+i+": "+t);return this}function Cn(e){return new this(e).cos()}function bn(e){return new this(e).cosh()}function $e(e){var n,i,t;function s(r){var o,u,c,f=this;if(!(f instanceof s))return new s(r);if(f.constructor=s,Ae(r)){f.s=r.s,w?!r.d||r.e>s.maxE?(f.e=NaN,f.d=null):r.e<s.minE?(f.e=0,f.d=[0]):(f.e=r.e,f.d=r.d.slice()):(f.e=r.e,f.d=r.d?r.d.slice():r.d);return}if(c=typeof r,c==="number"){if(r===0){f.s=1/r<0?-1:1,f.e=0,f.d=[0];return}if(r<0?(r=-r,f.s=-1):f.s=1,r===~~r&&r<1e7){for(o=0,u=r;u>=10;u/=10)o++;w?o>s.maxE?(f.e=NaN,f.d=null):o<s.minE?(f.e=0,f.d=[0]):(f.e=o,f.d=[r]):(f.e=o,f.d=[r]);return}if(r*0!==0){r||(f.s=NaN),f.e=NaN,f.d=null;return}return re(f,r.toString())}if(c==="string")return(u=r.charCodeAt(0))===45?(r=r.slice(1),f.s=-1):(u===43&&(r=r.slice(1)),f.s=1),Fe.test(r)?re(f,r):cn(f,r);if(c==="bigint")return r<0?(r=-r,f.s=-1):f.s=1,re(f,r.toString());throw Error($+r)}if(s.prototype=h,s.ROUND_UP=0,s.ROUND_DOWN=1,s.ROUND_CEIL=2,s.ROUND_FLOOR=3,s.ROUND_HALF_UP=4,s.ROUND_HALF_DOWN=5,s.ROUND_HALF_EVEN=6,s.ROUND_HALF_CEIL=7,s.ROUND_HALF_FLOOR=8,s.EUCLID=9,s.config=s.set=Mn,s.clone=$e,s.isDecimal=Ae,s.abs=an,s.acos=dn,s.acosh=hn,s.add=pn,s.asin=gn,s.asinh=mn,s.atan=wn,s.atanh=Nn,s.atan2=vn,s.cbrt=En,s.ceil=kn,s.clamp=Sn,s.cos=Cn,s.cosh=bn,s.div=Pn,s.exp=On,s.floor=Rn,s.hypot=An,s.ln=qn,s.log=_n,s.log10=Dn,s.log2=Tn,s.max=Fn,s.min=Ln,s.mod=In,s.mul=Zn,s.pow=Un,s.random=Bn,s.round=$n,s.sign=Hn,s.sin=Vn,s.sinh=jn,s.sqrt=Wn,s.sub=Gn,s.sum=Jn,s.tan=Xn,s.tanh=Kn,s.trunc=Qn,e===void 0&&(e={}),e&&e.defaults!==!0)for(t=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],n=0;n<t.length;)e.hasOwnProperty(i=t[n++])||(e[i]=this[i]);return s.config(e),s}function Pn(e,n){return new this(e).div(n)}function On(e){return new this(e).exp()}function Rn(e){return p(e=new this(e),e.e+1,3)}function An(){var e,n,i=new this(0);for(w=!1,e=0;e<arguments.length;)if(n=new this(arguments[e++]),n.d)i.d&&(i=i.plus(n.times(n)));else{if(n.s)return w=!0,new this(1/0);i=n}return w=!0,i.sqrt()}function Ae(e){return e instanceof Y||e&&e.toStringTag===De||!1}function qn(e){return new this(e).ln()}function _n(e,n){return new this(e).log(n)}function Tn(e){return new this(e).log(2)}function Dn(e){return new this(e).log(10)}function Fn(){return Ze(this,arguments,-1)}function Ln(){return Ze(this,arguments,1)}function In(e,n){return new this(e).mod(n)}function Zn(e,n){return new this(e).mul(n)}function Un(e,n){return new this(e).pow(n)}function Bn(e){var n,i,t,s,r=0,o=new this(1),u=[];if(e===void 0?e=this.precision:q(e,1,H),t=Math.ceil(e/m),this.crypto)if(crypto.getRandomValues)for(n=crypto.getRandomValues(new Uint32Array(t));r<t;)s=n[r],s>=429e7?n[r]=crypto.getRandomValues(new Uint32Array(1))[0]:u[r++]=s%1e7;else if(crypto.randomBytes){for(n=crypto.randomBytes(t*=4);r<t;)s=n[r]+(n[r+1]<<8)+(n[r+2]<<16)+((n[r+3]&127)<<24),s>=214e7?crypto.randomBytes(4).copy(n,r):(u.push(s%1e7),r+=4);r=t/4}else throw Error(Te);else for(;r<t;)u[r++]=Math.random()*1e7|0;for(t=u[--r],e%=m,t&&e&&(s=C(10,m-e),u[r]=(t/s|0)*s);u[r]===0;r--)u.pop();if(r<0)i=0,u=[0];else{for(i=-1;u[0]===0;i-=m)u.shift();for(t=1,s=u[0];s>=10;s/=10)t++;t<m&&(i-=m-t)}return o.e=i,o.d=u,o}function $n(e){return p(e=new this(e),e.e+1,this.rounding)}function Hn(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function Vn(e){return new this(e).sin()}function jn(e){return new this(e).sinh()}function Wn(e){return new this(e).sqrt()}function Gn(e,n){return new this(e).sub(n)}function Jn(){var e=0,n=arguments,i=new this(n[e]);for(w=!1;i.s&&++e<n.length;)i=i.plus(n[e]);return w=!0,p(i,this.precision,this.rounding)}function Xn(e){return new this(e).tan()}function Kn(e){return new this(e).tanh()}function Qn(e){return p(e=new this(e),e.e+1,1)}h[Symbol.for("nodejs.util.inspect.custom")]=h.toString;h[Symbol.toStringTag]="Decimal";var Y=h.constructor=$e(ke);se=new Y(se);oe=new Y(oe);var Yn=Y;export{Yn as Decimal,Pe as Public,nn as getRuntime,Je as makeStrictEnum,We as objectEnumValues};
/*! Bundled license information:

decimal.js/decimal.mjs:
  (*!
   *  decimal.js v10.5.0
   *  An arbitrary-precision Decimal type for JavaScript.
   *  https://github.com/MikeMcl/decimal.js
   *  Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>
   *  MIT Licence
   *)
*/
//# sourceMappingURL=index-browser.mjs.map
